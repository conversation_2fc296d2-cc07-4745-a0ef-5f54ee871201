﻿using LeadHistoryMigration.Application.ExistingHistory;
using System.Linq.Expressions;

namespace LeadHistoryMigration.Application.Interfaces
{
    public interface ILeadHistoryRepository
    {
        Task<LeadHistory?> GetByIdAsync(Guid id, bool includeDeleted = false);
        Task<List<LeadHistory>> GetByLeadIdAsync(Guid leadId, bool includeDeleted = false);
        Task<LeadHistory?> GetLatestVersionByLeadIdAsync(Guid leadId);
        Task<List<LeadHistory>> GetAllAsync(int pageNumber = 1, int pageSize = 100, bool includeDeleted = false);
        Task<List<LeadHistory>> GetByUserIdAsync(Guid userId, bool includeDeleted = false);
        Task<List<LeadHistory>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, bool includeDeleted = false);
        Task<int> GetCountAsync(bool includeDeleted = false);
        Task<List<LeadHistory>> FindAsync(Expression<Func<LeadHistory, bool>> predicate, int pageNumber = 1, int pageSize = 100);
    }
}
