using LeadHistoryMigration.Application.NewLeadHistory;

namespace LeadHistoryMigration.Application.Interfaces
{
    /// <summary>
    /// Repository interface for LeadHistoryCold entities (archival storage).
    /// Provides data access for rarely accessed historical lead history records.
    /// </summary>
    public interface ILeadHistoryColdRepository
    {
        /// <summary>
        /// Gets a lead history cold record by its ID.
        /// </summary>
        Task<LeadHistoryCold?> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets all lead history cold records for a specific lead ID.
        /// </summary>
        Task<List<LeadHistoryCold>> GetByLeadIdAsync(Guid leadId, int pageNumber = 1, int pageSize = 100);

        /// <summary>
        /// Gets lead history cold records by group key.
        /// </summary>
        Task<List<LeadHistoryCold>> GetByGroupKeyAsync(Guid groupKey);

        /// <summary>
        /// Gets lead history cold records within a date range.
        /// </summary>
        Task<List<LeadHistoryCold>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int pageNumber = 1, int pageSize = 100);

        /// <summary>
        /// Gets the total count of lead history cold records.
        /// </summary>
        Task<int> GetCountAsync();

        /// <summary>
        /// Inserts a single lead history cold record.
        /// </summary>
        Task<int> InsertAsync(LeadHistoryCold record);

        /// <summary>
        /// Inserts multiple lead history cold records using bulk operations.
        /// </summary>
        Task<int> BulkInsertAsync(IEnumerable<LeadHistoryCold> records);

        /// <summary>
        /// Updates a lead history cold record.
        /// </summary>
        Task<int> UpdateAsync(LeadHistoryCold record);

        /// <summary>
        /// Soft deletes a lead history cold record.
        /// </summary>
        Task<int> SoftDeleteAsync(Guid id);

        /// <summary>
        /// Hard deletes a lead history cold record.
        /// </summary>
        Task<int> DeleteAsync(Guid id);

        /// <summary>
        /// Permanently purges old records from cold storage.
        /// </summary>
        Task<int> PurgeOldRecordsAsync(DateTime cutoffDate);
    }
}
