using LeadHistoryMigration.Application.ExistingHistory;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Infra.NewLeadHistory;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LeadHistoryMigration.Tests
{
    /// <summary>
    /// Comprehensive unit tests for LeadHistoryTransformer
    /// These tests cover critical functionality, edge cases, and performance scenarios
    /// </summary>
    [TestClass]
    public class LeadHistoryTransformerTests
    {
        private LeadHistoryTransformer _transformer;
        private TransformationOptions _defaultOptions;

        [TestInitialize]
        public void Setup()
        {
            _defaultOptions = new TransformationOptions
            {
                StrictValidation = true,
                EnableVerboseLogging = false,
                ContinueOnError = true,
                EnableStringCaching = true
            };
            _transformer = new LeadHistoryTransformer(_defaultOptions);
        }

        #region Core Transformation Tests

        [TestMethod]
        public void TransformToFieldWise_ValidLeadHistory_ReturnsCorrectFieldWiseRecords()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.Name = new Dictionary<int, string>
            {
                { 1, "John Doe" },
                { 2, "John <PERSON>" }
            };

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count > 0);
            
            var nameChanges = result.Where(r => r.FieldName == "Name").ToList();
            Assert.AreEqual(1, nameChanges.Count);
            Assert.AreEqual(null, nameChanges[0].OldValue);
            Assert.AreEqual("John Doe", nameChanges[0].NewValue);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void TransformToFieldWise_NullLeadHistory_ThrowsArgumentNullException()
        {
            // Act
            _transformer.TransformToFieldWise(null);
        }

        [TestMethod]
        public void TransformToFieldWise_EmptyDictionaries_ReturnsEmptyList()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            // All dictionaries are null by default

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        public void TransformToFieldWise_MultipleVersions_CreatesCorrectSequence()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.Name = new Dictionary<int, string>
            {
                { 1, "John" },
                { 2, "John Doe" },
                { 3, "John Smith" }
            };

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            var nameChanges = result.Where(r => r.FieldName == "Name").OrderBy(r => r.Version).ToList();
            Assert.AreEqual(2, nameChanges.Count);
            
            // First change: null -> "John"
            Assert.AreEqual(null, nameChanges[0].OldValue);
            Assert.AreEqual("John", nameChanges[0].NewValue);
            Assert.AreEqual(1, nameChanges[0].Version);
            
            // Second change: "John" -> "John Doe"
            Assert.AreEqual("John", nameChanges[1].OldValue);
            Assert.AreEqual("John Doe", nameChanges[1].NewValue);
            Assert.AreEqual(2, nameChanges[1].Version);
        }

        #endregion

        #region Data Type Tests

        [TestMethod]
        public void TransformToFieldWise_BooleanFields_HandledCorrectly()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.IsHotLead = new Dictionary<int, bool>
            {
                { 1, false },
                { 2, true }
            };

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            var hotLeadChanges = result.Where(r => r.FieldName == "Is Hot Lead").ToList();
            Assert.AreEqual(1, hotLeadChanges.Count);
            Assert.AreEqual("False", hotLeadChanges[0].OldValue);
            Assert.AreEqual("True", hotLeadChanges[0].NewValue);
        }

        [TestMethod]
        public void TransformToFieldWise_NumericFields_FormattedCorrectly()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.LowerBudget = new Dictionary<int, long>
            {
                { 1, 100000 },
                { 2, 150000 }
            };

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            var budgetChanges = result.Where(r => r.FieldName == "Lower Budget").ToList();
            Assert.AreEqual(1, budgetChanges.Count);
            Assert.AreEqual("100000", budgetChanges[0].OldValue);
            Assert.AreEqual("150000", budgetChanges[0].NewValue);
        }

        [TestMethod]
        public void TransformToFieldWise_DateTimeFields_FormattedCorrectly()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            var date1 = new DateTime(2023, 1, 1, 10, 30, 0);
            var date2 = new DateTime(2023, 1, 2, 15, 45, 0);
            
            leadHistory.ScheduledDate = new Dictionary<int, DateTime?>
            {
                { 1, date1 },
                { 2, date2 }
            };

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            var dateChanges = result.Where(r => r.FieldName == "Scheduled Date").ToList();
            Assert.AreEqual(1, dateChanges.Count);
            Assert.AreEqual("01/01/2023 10:30:00", dateChanges[0].OldValue);
            Assert.AreEqual("01/02/2023 15:45:00", dateChanges[0].NewValue);
        }

        [TestMethod]
        public void TransformToFieldWise_EnumFields_ConvertedToString()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.LeadSource = new Dictionary<int, LeadSource>
            {
                { 1, LeadSource.Direct },
                { 2, LeadSource.Facebook }
            };

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            var sourceChanges = result.Where(r => r.FieldName == "Lead Source").ToList();
            Assert.AreEqual(1, sourceChanges.Count);
            Assert.AreEqual("Direct", sourceChanges[0].OldValue);
            Assert.AreEqual("Facebook", sourceChanges[0].NewValue);
        }

        #endregion

        #region Async Processing Tests

        [TestMethod]
        public async Task TransformMultipleToFieldWiseAsync_SmallCollection_ProcessedSequentially()
        {
            // Arrange
            var leadHistories = CreateMultipleLeadHistories(5);

            // Act
            var result = await _transformer.TransformMultipleToFieldWiseAsync(leadHistories);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count > 0);
        }

        [TestMethod]
        public async Task TransformMultipleToFieldWiseAsync_LargeCollection_ProcessedInParallel()
        {
            // Arrange
            var leadHistories = CreateMultipleLeadHistories(50);

            // Act
            var result = await _transformer.TransformMultipleToFieldWiseAsync(leadHistories);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count > 0);
        }

        [TestMethod]
        public async Task TransformMultipleToFieldWiseAsync_EmptyCollection_ReturnsEmptyList()
        {
            // Arrange
            var leadHistories = new List<LeadHistory>();

            // Act
            var result = await _transformer.TransformMultipleToFieldWiseAsync(leadHistories);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public async Task TransformMultipleToFieldWiseAsync_NullCollection_ThrowsArgumentNullException()
        {
            // Act
            await _transformer.TransformMultipleToFieldWiseAsync(null);
        }

        #endregion

        #region Validation Tests

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void TransformToFieldWise_InvalidLeadId_ThrowsArgumentException()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.LeadId = Guid.Empty;

            // Act
            _transformer.TransformToFieldWise(leadHistory);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void TransformToFieldWise_InvalidCreatedBy_ThrowsArgumentException()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.CreatedBy = Guid.Empty;

            // Act
            _transformer.TransformToFieldWise(leadHistory);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void TransformToFieldWise_FutureCreatedDate_ThrowsArgumentException()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.CreatedDate = DateTime.UtcNow.AddDays(2);

            // Act
            _transformer.TransformToFieldWise(leadHistory);
        }

        #endregion

        #region Helper Methods

        private LeadHistory CreateValidLeadHistory()
        {
            return new LeadHistory
            {
                Id = Guid.NewGuid(),
                LeadId = Guid.NewGuid(),
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.UtcNow.AddDays(-1),
                UserId = Guid.NewGuid(),
                IsDeleted = false
            };
        }

        private List<LeadHistory> CreateMultipleLeadHistories(int count)
        {
            var histories = new List<LeadHistory>();
            for (int i = 0; i < count; i++)
            {
                var history = CreateValidLeadHistory();
                history.Name = new Dictionary<int, string>
                {
                    { 1, $"Lead {i}" },
                    { 2, $"Lead {i} Updated" }
                };
                histories.Add(history);
            }
            return histories;
        }

        #endregion

        #region Performance Tests

        [TestMethod]
        public void TransformToFieldWise_LargeDataSet_PerformsWithinTimeLimit()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            var largeDataSet = new Dictionary<int, string>();
            for (int i = 1; i <= 1000; i++)
            {
                largeDataSet[i] = $"Value {i}";
            }
            leadHistory.Name = largeDataSet;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = _transformer.TransformToFieldWise(leadHistory);

            // Assert
            stopwatch.Stop();
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1000, "Transformation should complete within 1 second");
            Assert.IsTrue(result.Count > 0);
        }

        [TestMethod]
        public void TransformToFieldWise_StringCaching_ImprovedPerformance()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.Name = new Dictionary<int, string>
            {
                { 1, "Repeated Value" },
                { 2, "Repeated Value" },
                { 3, "Different Value" },
                { 4, "Repeated Value" }
            };

            var optionsWithCaching = new TransformationOptions { EnableStringCaching = true };
            var transformerWithCaching = new LeadHistoryTransformer(optionsWithCaching);

            var optionsWithoutCaching = new TransformationOptions { EnableStringCaching = false };
            var transformerWithoutCaching = new LeadHistoryTransformer(optionsWithoutCaching);

            // Act & Assert - Both should work, caching version should be faster for repeated values
            var resultWithCaching = transformerWithCaching.TransformToFieldWise(leadHistory);
            var resultWithoutCaching = transformerWithoutCaching.TransformToFieldWise(leadHistory);

            Assert.AreEqual(resultWithCaching.Count, resultWithoutCaching.Count);
        }

        #endregion

        #region Configuration Tests

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_NullOptions_ThrowsArgumentNullException()
        {
            // Act
            new LeadHistoryTransformer(null);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void Constructor_InvalidOptions_ThrowsInvalidOperationException()
        {
            // Arrange
            var invalidOptions = new TransformationOptions
            {
                ParallelProcessingThreshold = -1 // Invalid value
            };

            // Act
            new LeadHistoryTransformer(invalidOptions);
        }

        [TestMethod]
        public void TransformationOptions_DefaultValues_AreValid()
        {
            // Arrange & Act
            var options = TransformationOptions.Default;

            // Assert
            Assert.IsTrue(options.ParallelProcessingThreshold > 0);
            Assert.IsTrue(options.MaxBatchSize > 0);
            Assert.IsTrue(options.EstimatedRecordsPerLead > 0);
            Assert.IsFalse(string.IsNullOrWhiteSpace(options.DefaultTenantId));
            Assert.IsFalse(string.IsNullOrWhiteSpace(options.DateTimeFormat));
        }

        #endregion

        #region Error Handling Tests

        [TestMethod]
        public async Task TransformMultipleToFieldWiseAsync_WithErrors_ContinuesProcessing()
        {
            // Arrange
            var leadHistories = new List<LeadHistory>
            {
                CreateValidLeadHistory(),
                null, // This will cause an error
                CreateValidLeadHistory()
            };

            var options = new TransformationOptions { ContinueOnError = true };
            var transformer = new LeadHistoryTransformer(options);

            // Act
            var result = await transformer.TransformMultipleToFieldWiseAsync(leadHistories);

            // Assert - Should process valid records despite null record
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void TransformToFieldWise_NegativeVersionNumbers_HandledGracefully()
        {
            // Arrange
            var leadHistory = CreateValidLeadHistory();
            leadHistory.Name = new Dictionary<int, string>
            {
                { -1, "Invalid Version" },
                { 1, "Valid Version" }
            };

            // This should trigger validation error in strict mode
            try
            {
                // Act
                var result = _transformer.TransformToFieldWise(leadHistory);
                Assert.Fail("Expected validation exception for negative version numbers");
            }
            catch (ArgumentException ex)
            {
                // Assert
                Assert.IsTrue(ex.Message.Contains("negative version numbers"));
            }
        }

        #endregion
    }

    /// <summary>
    /// Integration tests for LeadHistoryTransformer with real-world scenarios
    /// </summary>
    [TestClass]
    public class LeadHistoryTransformerIntegrationTests
    {
        [TestMethod]
        public async Task TransformLargeDataset_RealWorldScenario_CompletesSuccessfully()
        {
            // Arrange
            var options = new TransformationOptions
            {
                ParallelProcessingThreshold = 10,
                MaxDegreeOfParallelism = Environment.ProcessorCount,
                EnableStringCaching = true,
                ContinueOnError = true
            };
            var transformer = new LeadHistoryTransformer(options);

            var leadHistories = CreateRealWorldDataset(100);

            // Act
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await transformer.TransformMultipleToFieldWiseAsync(leadHistories);
            stopwatch.Stop();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count > 0);
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 10000, "Should complete within 10 seconds");

            // Verify data integrity
            var groupKeys = result.Select(r => r.GroupKey).Distinct().Count();
            Assert.AreEqual(leadHistories.Count, groupKeys, "Each lead should have a unique group key");
        }

        private List<LeadHistory> CreateRealWorldDataset(int count)
        {
            var histories = new List<LeadHistory>();
            var random = new Random(42); // Fixed seed for reproducible tests

            for (int i = 0; i < count; i++)
            {
                var history = new LeadHistory
                {
                    Id = Guid.NewGuid(),
                    LeadId = Guid.NewGuid(),
                    CreatedBy = Guid.NewGuid(),
                    CreatedDate = DateTime.UtcNow.AddDays(-random.Next(1, 365)),
                    UserId = Guid.NewGuid(),
                    IsDeleted = false
                };

                // Add realistic field changes
                history.Name = CreateVersionedData($"Lead {i}", random);
                history.ContactNo = CreateVersionedData($"+1234567{i:D3}", random);
                history.Email = CreateVersionedData($"lead{i}@example.com", random);
                history.IsHotLead = CreateVersionedBoolData(random);
                history.LowerBudget = CreateVersionedLongData(100000 + i * 1000, random);

                histories.Add(history);
            }

            return histories;
        }

        private Dictionary<int, string> CreateVersionedData(string baseValue, Random random)
        {
            var versions = random.Next(1, 5);
            var data = new Dictionary<int, string>();

            for (int v = 1; v <= versions; v++)
            {
                data[v] = $"{baseValue}_v{v}";
            }

            return data;
        }

        private Dictionary<int, bool> CreateVersionedBoolData(Random random)
        {
            var versions = random.Next(1, 3);
            var data = new Dictionary<int, bool>();

            for (int v = 1; v <= versions; v++)
            {
                data[v] = random.Next(2) == 1;
            }

            return data;
        }

        private Dictionary<int, long> CreateVersionedLongData(long baseValue, Random random)
        {
            var versions = random.Next(1, 4);
            var data = new Dictionary<int, long>();

            for (int v = 1; v <= versions; v++)
            {
                data[v] = baseValue + (v * random.Next(1000, 10000));
            }

            return data;
        }
    }
}
