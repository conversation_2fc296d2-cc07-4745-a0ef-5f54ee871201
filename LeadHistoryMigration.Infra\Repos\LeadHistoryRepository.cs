﻿using LeadHistoryMigration.Application.ExistingHistory;
using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Infra.Database;
using Dapper;
using System.Data;
using System.Text;
using System.Linq.Expressions;
using Microsoft.Extensions.Logging;
using Npgsql;
using System.Diagnostics;

namespace LeadHistoryMigration.Infra.Repos
{
    /// <summary>
    /// Dapper-based repository for LeadHistory entities with optimized PostgreSQL queries.
    /// Provides high-performance data access with proper connection management and comprehensive error handling.
    /// </summary>
    public class LeadHistoryRepository : ILeadHistoryRepository
    {
        private readonly IDapperConnectionFactory _connectionFactory;
        private readonly ILogger<LeadHistoryRepository> _logger;

        public LeadHistoryRepository(IDapperConnectionFactory connectionFactory, ILogger<LeadHistoryRepository> logger)
        {
            _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a lead history record by its ID using optimized Dapper query.
        /// </summary>
        public async Task<LeadHistory?> GetByIdAsync(Guid id, bool includeDeleted = false)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistories""
                WHERE ""Id"" = @Id
                AND (@IncludeDeleted = true OR ""IsDeleted"" = false)";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetByIdAsync for LeadHistory with Id: {Id}, IncludeDeleted: {IncludeDeleted}",
                    id, includeDeleted);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryFirstOrDefaultAsync<LeadHistory>(sql, new { Id = id, IncludeDeleted = includeDeleted });

                stopwatch.Stop();
                _logger.LogDebug("GetByIdAsync completed successfully in {ElapsedMs}ms for Id: {Id}",
                    stopwatch.ElapsedMilliseconds, id);

                return result;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByIdAsync for Id: {Id}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history record with ID {id}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByIdAsync for Id: {Id}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    id, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history record with ID {id}", ex);
            }
            catch (InvalidOperationException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Invalid operation in GetByIdAsync for Id: {Id}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByIdAsync for Id: {Id}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history record with ID {id}", ex);
            }
        }

        /// <summary>
        /// Gets all lead history records for a specific lead ID with optimized sorting.
        /// </summary>
        public async Task<List<LeadHistory>> GetByLeadIdAsync(Guid leadId, bool includeDeleted = false)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistories""
                WHERE ""LeadId"" = @LeadId
                AND (@IncludeDeleted = true OR ""IsDeleted"" = false)
                ORDER BY ""CurrentVersion"" DESC";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetByLeadIdAsync for LeadId: {LeadId}, IncludeDeleted: {IncludeDeleted}",
                    leadId, includeDeleted);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryAsync<LeadHistory>(sql, new { LeadId = leadId, IncludeDeleted = includeDeleted });
                var resultList = result.ToList();

                stopwatch.Stop();
                _logger.LogDebug("GetByLeadIdAsync completed successfully in {ElapsedMs}ms for LeadId: {LeadId}. Records returned: {RecordCount}",
                    stopwatch.ElapsedMilliseconds, leadId, resultList.Count);

                return resultList;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByLeadIdAsync for LeadId: {LeadId}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history records for lead ID {leadId}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByLeadIdAsync for LeadId: {LeadId}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    leadId, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history records for lead ID {leadId}", ex);
            }
            catch (InvalidOperationException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Invalid operation in GetByLeadIdAsync for LeadId: {LeadId}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, stopwatch.ElapsedMilliseconds, ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByLeadIdAsync for LeadId: {LeadId}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history records for lead ID {leadId}", ex);
            }
        }

        /// <summary>
        /// Gets the latest version of a lead history record for a specific lead ID.
        /// </summary>
        public async Task<LeadHistory?> GetLatestVersionByLeadIdAsync(Guid leadId)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistories""
                WHERE ""LeadId"" = @LeadId AND ""IsDeleted"" = false
                ORDER BY ""CurrentVersion"" DESC
                LIMIT 1";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetLatestVersionByLeadIdAsync for LeadId: {LeadId}", leadId);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryFirstOrDefaultAsync<LeadHistory>(sql, new { LeadId = leadId });

                stopwatch.Stop();
                _logger.LogDebug("GetLatestVersionByLeadIdAsync completed successfully in {ElapsedMs}ms for LeadId: {LeadId}",
                    stopwatch.ElapsedMilliseconds, leadId);

                return result;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetLatestVersionByLeadIdAsync for LeadId: {LeadId}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve latest lead history record for lead ID {leadId}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetLatestVersionByLeadIdAsync for LeadId: {LeadId}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    leadId, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving latest lead history record for lead ID {leadId}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetLatestVersionByLeadIdAsync for LeadId: {LeadId}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving latest lead history record for lead ID {leadId}", ex);
            }
        }

        /// <summary>
        /// Gets all lead history records with pagination using optimized Dapper query.
        /// </summary>
        public async Task<List<LeadHistory>> GetAllAsync(int pageNumber = 1, int pageSize = 1, bool includeDeleted = false)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistories""
                WHERE (@IncludeDeleted = true OR ""IsDeleted"" = false)
                ORDER BY ""CreatedDate"" DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                var offset = (pageNumber - 1) * pageSize;
                _logger.LogDebug("Executing GetAllAsync with PageNumber: {PageNumber}, PageSize: {PageSize}, IncludeDeleted: {IncludeDeleted}",
                    pageNumber, pageSize, includeDeleted);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryAsync<LeadHistory>(sql, new { IncludeDeleted = includeDeleted, Offset = offset, PageSize = pageSize });
                var resultList = result.ToList();

                stopwatch.Stop();
                _logger.LogDebug("GetAllAsync completed successfully in {ElapsedMs}ms. Records returned: {RecordCount}",
                    stopwatch.ElapsedMilliseconds, resultList.Count);

                return resultList;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetAllAsync with PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    pageNumber, pageSize, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history records (page {pageNumber}, size {pageSize})", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetAllAsync with PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    pageNumber, pageSize, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history records (page {pageNumber}, size {pageSize})", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetAllAsync with PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    pageNumber, pageSize, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history records (page {pageNumber}, size {pageSize})", ex);
            }
        }

        /// <summary>
        /// Gets all lead history records for a specific user ID.
        /// </summary>
        public async Task<List<LeadHistory>> GetByUserIdAsync(Guid userId, bool includeDeleted = false)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistories""
                WHERE ""UserId"" = @UserId
                AND (@IncludeDeleted = true OR ""IsDeleted"" = false)
                ORDER BY ""CreatedDate"" DESC";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetByUserIdAsync for UserId: {UserId}, IncludeDeleted: {IncludeDeleted}",
                    userId, includeDeleted);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryAsync<LeadHistory>(sql, new { UserId = userId, IncludeDeleted = includeDeleted });
                var resultList = result.ToList();

                stopwatch.Stop();
                _logger.LogDebug("GetByUserIdAsync completed successfully in {ElapsedMs}ms for UserId: {UserId}. Records returned: {RecordCount}",
                    stopwatch.ElapsedMilliseconds, userId, resultList.Count);

                return resultList;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByUserIdAsync for UserId: {UserId}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    userId, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history records for user ID {userId}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByUserIdAsync for UserId: {UserId}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    userId, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history records for user ID {userId}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByUserIdAsync for UserId: {UserId}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    userId, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history records for user ID {userId}", ex);
            }
        }

        /// <summary>
        /// Gets lead history records within a specific date range.
        /// </summary>
        public async Task<List<LeadHistory>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, bool includeDeleted = false)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistories""
                WHERE ""CreatedDate"" >= @StartDate AND ""CreatedDate"" <= @EndDate
                AND (@IncludeDeleted = true OR ""IsDeleted"" = false)
                ORDER BY ""CreatedDate"" DESC";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetByDateRangeAsync from {StartDate} to {EndDate}, IncludeDeleted: {IncludeDeleted}",
                    startDate, endDate, includeDeleted);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryAsync<LeadHistory>(sql, new { StartDate = startDate, EndDate = endDate, IncludeDeleted = includeDeleted });
                var resultList = result.ToList();

                stopwatch.Stop();
                _logger.LogDebug("GetByDateRangeAsync completed successfully in {ElapsedMs}ms. Records returned: {RecordCount}",
                    stopwatch.ElapsedMilliseconds, resultList.Count);

                return resultList;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByDateRangeAsync from {StartDate} to {EndDate}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    startDate, endDate, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history records for date range {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByDateRangeAsync from {StartDate} to {EndDate}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    startDate, endDate, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history records for date range {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByDateRangeAsync from {StartDate} to {EndDate}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    startDate, endDate, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history records for date range {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}", ex);
            }
        }

        /// <summary>
        /// Gets the total count of lead history records.
        /// </summary>
        public async Task<int> GetCountAsync(bool includeDeleted = false)
        {
            const string sql = @"
                SELECT COUNT(*) FROM ""LeadratBlack"".""LeadHistories""
                WHERE (@IncludeDeleted = true OR ""IsDeleted"" = false)";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetCountAsync with IncludeDeleted: {IncludeDeleted}", includeDeleted);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QuerySingleAsync<int>(sql, new { IncludeDeleted = includeDeleted });

                stopwatch.Stop();
                _logger.LogDebug("GetCountAsync completed successfully in {ElapsedMs}ms. Count: {Count}",
                    stopwatch.ElapsedMilliseconds, result);

                return result;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetCountAsync. Operation: SELECT COUNT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException("Failed to retrieve lead history record count", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetCountAsync. Operation: SELECT COUNT. ElapsedMs: {ElapsedMs}",
                    stopwatch.ElapsedMilliseconds);
                throw new TimeoutException("Database operation timed out while retrieving lead history record count", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetCountAsync. Operation: SELECT COUNT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException("An unexpected error occurred while retrieving lead history record count", ex);
            }
        }

        /// <summary>
        /// Finds lead history records using a predicate expression.
        /// Note: This method maintains compatibility with the existing interface but uses a fallback approach
        /// since Dapper doesn't support Expression trees directly. For better performance, use specific methods.
        /// </summary>
        public async Task<List<LeadHistory>> FindAsync(Expression<Func<LeadHistory, bool>> predicate, int pageNumber = 1, int pageSize = 100)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing FindAsync with PageNumber: {PageNumber}, PageSize: {PageSize}", pageNumber, pageSize);
                _logger.LogWarning("FindAsync uses in-memory filtering which may impact performance for large datasets. Consider using specific query methods instead.");

                // For complex queries with expressions, we fall back to getting all records and filtering in memory
                // This is not optimal for large datasets, but maintains interface compatibility
                // TODO: Consider creating specific query methods for common use cases

                const string sql = @"
                    SELECT * FROM ""LeadratBlack"".""LeadHistories""
                    ORDER BY ""CreatedDate"" DESC";

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var allRecords = await connection.QueryAsync<LeadHistory>(sql);

                // Apply the predicate in memory and paginate
                var filteredRecords = allRecords.AsQueryable()
                    .Where(predicate)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                stopwatch.Stop();
                _logger.LogDebug("FindAsync completed successfully in {ElapsedMs}ms. Total records fetched: {TotalRecords}, Filtered records returned: {FilteredCount}",
                    stopwatch.ElapsedMilliseconds, allRecords.Count(), filteredRecords.Count);

                return filteredRecords;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in FindAsync with PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    pageNumber, pageSize, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to execute find query (page {pageNumber}, size {pageSize})", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in FindAsync with PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    pageNumber, pageSize, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while executing find query (page {pageNumber}, size {pageSize})", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in FindAsync with PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    pageNumber, pageSize, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while executing find query (page {pageNumber}, size {pageSize})", ex);
            }
        }
    }
}
