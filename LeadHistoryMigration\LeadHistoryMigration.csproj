﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>.</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
      <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LeadHistoryMigration.Infra\LeadHistoryMigration.Infra.csproj" />
  </ItemGroup>

</Project>
