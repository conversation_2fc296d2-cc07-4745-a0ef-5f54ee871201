using LeadHistoryMigration.Application.NewLeadHistory;

namespace LeadHistoryMigration.Application.Interfaces
{
    /// <summary>
    /// Repository interface for LeadHistoryHot entities with bulk operations support.
    /// Provides high-performance data access for transformed lead history records.
    /// </summary>
    public interface ILeadHistoryHotRepository
    {
        /// <summary>
        /// Gets a lead history hot record by its ID.
        /// </summary>
        Task<LeadHistoryHot?> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets all lead history hot records for a specific lead ID.
        /// </summary>
        Task<List<LeadHistoryHot>> GetByLeadIdAsync(Guid leadId, int pageNumber = 1, int pageSize = 100);

        /// <summary>
        /// Gets lead history hot records by group key (transformation batch).
        /// </summary>
        Task<List<LeadHistoryHot>> GetByGroupKeyAsync(Guid groupKey);

        /// <summary>
        /// Gets lead history hot records for a specific field name and lead ID.
        /// </summary>
        Task<List<LeadHistoryHot>> GetByFieldNameAsync(Guid leadId, string fieldName);

        /// <summary>
        /// Gets lead history hot records within a date range.
        /// </summary>
        Task<List<LeadHistoryHot>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int pageNumber = 1, int pageSize = 100);

        /// <summary>
        /// Gets the total count of lead history hot records.
        /// </summary>
        Task<int> GetCountAsync();

        /// <summary>
        /// Gets the count of records for a specific lead ID.
        /// </summary>
        Task<int> GetCountByLeadIdAsync(Guid leadId);

        /// <summary>
        /// Inserts a single lead history hot record.
        /// </summary>
        Task<int> InsertAsync(LeadHistoryHot record);

        /// <summary>
        /// Inserts multiple lead history hot records using bulk operations for optimal performance.
        /// </summary>
        Task<int> BulkInsertAsync(IEnumerable<LeadHistoryHot> records);

        /// <summary>
        /// Updates a lead history hot record.
        /// </summary>
        Task<int> UpdateAsync(LeadHistoryHot record);

        /// <summary>
        /// Soft deletes a lead history hot record by setting IsDeleted = true.
        /// </summary>
        Task<int> SoftDeleteAsync(Guid id);

        /// <summary>
        /// Hard deletes a lead history hot record from the database.
        /// </summary>
        Task<int> DeleteAsync(Guid id);

        /// <summary>
        /// Deletes all records for a specific lead ID.
        /// </summary>
        Task<int> DeleteByLeadIdAsync(Guid leadId);

        /// <summary>
        /// Archives old records by moving them to warm storage.
        /// </summary>
        Task<int> ArchiveOldRecordsAsync(DateTime cutoffDate);
    }
}
