using System.Data;
using System.Text.Json;
using Dapper;
using System.Collections.Generic;
using LeadHistoryMigration.Application.ExistingHistory;

namespace LeadHistoryMigration.Infra.Database
{
    /// <summary>
    /// Custom Dapper type handlers for JSON deserialization of dictionary properties.
    /// Handles PostgreSQL JSONB columns that store IDictionary<int, T> data.
    /// </summary>
    public static class JsonTypeHandlers
    {
        /// <summary>
        /// Registers all JSON type handlers with Dapper.
        /// Call this method during application startup.
        /// </summary>
        public static void RegisterTypeHandlers()
        {
            // Basic type handlers
            SqlMapper.AddTypeHandler(new JsonDictionaryIntDateTimeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntNullableDateTimeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntGuidHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntStringHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntBoolHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntIntHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntLongHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntDoubleHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntFloatHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntDecimalHandler());

            // Enum type handlers
            SqlMapper.AddTypeHandler(new JsonDictionaryIntEnquiryTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntSaleTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntLeadSourceHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntContactTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntProfessionHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntUploadTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntBulkTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntLeadAssignmentTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntMaritalStatusTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntGenderHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntPossesionTypeHandler());
            SqlMapper.AddTypeHandler(new JsonDictionaryIntPurposeHandler());
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, DateTime> properties.
    /// </summary>
    public class JsonDictionaryIntDateTimeHandler : SqlMapper.TypeHandler<IDictionary<int, DateTime>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, DateTime>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, DateTime>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, DateTime>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, DateTime?> properties.
    /// </summary>
    public class JsonDictionaryIntNullableDateTimeHandler : SqlMapper.TypeHandler<IDictionary<int, DateTime?>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, DateTime?>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, DateTime?>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, DateTime?>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, Guid> properties.
    /// </summary>
    public class JsonDictionaryIntGuidHandler : SqlMapper.TypeHandler<IDictionary<int, Guid>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, Guid>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, Guid>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, Guid>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, string> properties.
    /// </summary>
    public class JsonDictionaryIntStringHandler : SqlMapper.TypeHandler<IDictionary<int, string>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, string>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, string>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, string>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, bool> properties.
    /// </summary>
    public class JsonDictionaryIntBoolHandler : SqlMapper.TypeHandler<IDictionary<int, bool>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, bool>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, bool>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, bool>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, int> properties.
    /// </summary>
    public class JsonDictionaryIntIntHandler : SqlMapper.TypeHandler<IDictionary<int, int>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, int>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, int>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, int>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, double> properties.
    /// </summary>
    public class JsonDictionaryIntDoubleHandler : SqlMapper.TypeHandler<IDictionary<int, double>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, double>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, double>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, double>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, float> properties.
    /// </summary>
    public class JsonDictionaryIntFloatHandler : SqlMapper.TypeHandler<IDictionary<int, float>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, float>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, float>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, float>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, long> properties.
    /// </summary>
    public class JsonDictionaryIntLongHandler : SqlMapper.TypeHandler<IDictionary<int, long>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, long>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, long>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, long>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, decimal> properties.
    /// </summary>
    public class JsonDictionaryIntDecimalHandler : SqlMapper.TypeHandler<IDictionary<int, decimal>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, decimal>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, decimal>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, decimal>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    // Enum Type Handlers

    /// <summary>
    /// Type handler for IDictionary<int, EnquiryType> properties.
    /// </summary>
    public class JsonDictionaryIntEnquiryTypeHandler : SqlMapper.TypeHandler<IDictionary<int, EnquiryType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, EnquiryType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, EnquiryType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, EnquiryType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, SaleType> properties.
    /// </summary>
    public class JsonDictionaryIntSaleTypeHandler : SqlMapper.TypeHandler<IDictionary<int, SaleType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, SaleType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, SaleType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, SaleType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, LeadSource> properties.
    /// </summary>
    public class JsonDictionaryIntLeadSourceHandler : SqlMapper.TypeHandler<IDictionary<int, LeadSource>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, LeadSource>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, LeadSource>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, LeadSource>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, ContactType> properties.
    /// </summary>
    public class JsonDictionaryIntContactTypeHandler : SqlMapper.TypeHandler<IDictionary<int, ContactType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, ContactType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, ContactType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, ContactType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, Profession> properties.
    /// </summary>
    public class JsonDictionaryIntProfessionHandler : SqlMapper.TypeHandler<IDictionary<int, Profession>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, Profession>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, Profession>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, Profession>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, UploadType> properties.
    /// </summary>
    public class JsonDictionaryIntUploadTypeHandler : SqlMapper.TypeHandler<IDictionary<int, UploadType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, UploadType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, UploadType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, UploadType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, BulkType> properties.
    /// </summary>
    public class JsonDictionaryIntBulkTypeHandler : SqlMapper.TypeHandler<IDictionary<int, BulkType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, BulkType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, BulkType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, BulkType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, LeadAssignmentType> properties.
    /// </summary>
    public class JsonDictionaryIntLeadAssignmentTypeHandler : SqlMapper.TypeHandler<IDictionary<int, LeadAssignmentType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, LeadAssignmentType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, LeadAssignmentType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, LeadAssignmentType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, MaritalStatusType> properties.
    /// </summary>
    public class JsonDictionaryIntMaritalStatusTypeHandler : SqlMapper.TypeHandler<IDictionary<int, MaritalStatusType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, MaritalStatusType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, MaritalStatusType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, MaritalStatusType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, Gender> properties.
    /// </summary>
    public class JsonDictionaryIntGenderHandler : SqlMapper.TypeHandler<IDictionary<int, Gender>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, Gender>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, Gender>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, Gender>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, PossesionType> properties.
    /// </summary>
    public class JsonDictionaryIntPossesionTypeHandler : SqlMapper.TypeHandler<IDictionary<int, PossesionType>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, PossesionType>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, PossesionType>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, PossesionType>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }

    /// <summary>
    /// Type handler for IDictionary<int, Purpose> properties.
    /// </summary>
    public class JsonDictionaryIntPurposeHandler : SqlMapper.TypeHandler<IDictionary<int, Purpose>?>
    {
        public override void SetValue(IDbDataParameter parameter, IDictionary<int, Purpose>? value)
        {
            parameter.Value = value == null ? DBNull.Value : JsonSerializer.Serialize(value);
        }

        public override IDictionary<int, Purpose>? Parse(object value)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var json = value.ToString();
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<Dictionary<int, Purpose>>(json);
            }
            catch (JsonException)
            {
                return null;
            }
        }
    }
}
