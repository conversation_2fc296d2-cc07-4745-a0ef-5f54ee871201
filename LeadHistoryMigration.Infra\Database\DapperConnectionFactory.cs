using Microsoft.Extensions.Options;
using Npgsql;
using System.Data;

namespace LeadHistoryMigration.Infra.Database
{
    /// <summary>
    /// PostgreSQL implementation of the Dapper connection factory.
    /// Provides optimized connection management for high-performance database operations.
    /// </summary>
    public class DapperConnectionFactory : IDapperConnectionFactory
    {
        private readonly DatabaseSettings _settings;

        public DapperConnectionFactory(IOptions<DatabaseSettings> settings)
        {
            _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
            
            if (string.IsNullOrWhiteSpace(_settings.ConnectionString))
                throw new ArgumentException("Connection string cannot be null or empty", nameof(settings));
        }

        /// <summary>
        /// Gets the connection string for the database.
        /// </summary>
        public string ConnectionString => _settings.ConnectionString;

        /// <summary>
        /// Creates a new PostgreSQL database connection.
        /// </summary>
        /// <returns>A new NpgsqlConnection instance</returns>
        public IDbConnection CreateConnection()
        {
            var connection = new NpgsqlConnection(_settings.ConnectionString);
            
            // Configure connection for optimal performance
            ConfigureConnection(connection);
            
            return connection;
        }

        /// <summary>
        /// Creates a new database connection and opens it asynchronously.
        /// </summary>
        /// <returns>An opened database connection instance</returns>
        public async Task<IDbConnection> CreateOpenConnectionAsync()
        {
            var connection = CreateConnection();
            
            try
            {
                await ((NpgsqlConnection)connection).OpenAsync();
                return connection;
            }
            catch
            {
                connection.Dispose();
                throw;
            }
        }

        /// <summary>
        /// Creates a new database connection with transaction support.
        /// </summary>
        /// <returns>A tuple containing the connection and transaction</returns>
        public async Task<(IDbConnection Connection, IDbTransaction Transaction)> CreateConnectionWithTransactionAsync()
        {
            var connection = await CreateOpenConnectionAsync();
            
            try
            {
                var transaction = connection.BeginTransaction();
                return (connection, transaction);
            }
            catch
            {
                connection.Dispose();
                throw;
            }
        }

        /// <summary>
        /// Configures the PostgreSQL connection for optimal performance.
        /// </summary>
        /// <param name="connection">The connection to configure</param>
        private void ConfigureConnection(NpgsqlConnection connection)
        {
            // Note: CommandTimeout is set via connection string, not directly on connection
            // Enable connection pooling optimizations
            var builder = new NpgsqlConnectionStringBuilder(connection.ConnectionString);

            // Set command timeout from settings
            builder.CommandTimeout = _settings.CommandTimeout;

            // Ensure pooling is enabled for better performance
            if (!builder.Pooling)
            {
                builder.Pooling = true;
            }

            // Set reasonable pool sizes if not specified
            if (builder.MinPoolSize == 0)
                builder.MinPoolSize = 3;

            if (builder.MaxPoolSize == 100) // Default value
                builder.MaxPoolSize = 50; // Reasonable limit for most applications

            // Update the connection string with optimized settings
            connection.ConnectionString = builder.ConnectionString;
        }
    }
}
