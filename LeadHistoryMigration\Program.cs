﻿using LeadHistoryMigration.Infra;
using LeadHistoryMigration.Infra.Database;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
namespace LeadHistoryMigration
{
    public class Program
    {
        static async Task Main(string[] args)
        {
            // Build configuration
            var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();

            var host = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // Register configuration
                services.AddSingleton<IConfiguration>(configuration);

                // Register database settings
                services.Configure<DatabaseSettings>(configuration.GetSection("DatabaseSettings"));

                // Register infrastructure services from another project
                services.AddInfra(configuration);

                // Register your main application class
                services.AddTransient<App>();
            })
            .Build();

            using (var scope = host.Services.CreateScope())
            {
                var serviceProvider = scope.ServiceProvider;

                try
                {
                    // Get your main app class and run it
                    var app = serviceProvider.GetRequiredService<App>();
                    await app.RunAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"An error occurred: {ex.Message}");
                }
            }
        }
    }
}
