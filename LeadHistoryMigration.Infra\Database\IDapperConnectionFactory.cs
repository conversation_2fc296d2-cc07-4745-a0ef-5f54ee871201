using System.Data;

namespace LeadHistoryMigration.Infra.Database
{
    /// <summary>
    /// Interface for creating database connections for Dapper operations.
    /// Provides connection management with proper disposal and transaction support.
    /// </summary>
    public interface IDapperConnectionFactory
    {
        /// <summary>
        /// Creates a new database connection.
        /// </summary>
        /// <returns>A new database connection instance</returns>
        IDbConnection CreateConnection();

        /// <summary>
        /// Creates a new database connection and opens it.
        /// </summary>
        /// <returns>An opened database connection instance</returns>
        Task<IDbConnection> CreateOpenConnectionAsync();

        /// <summary>
        /// Creates a new database connection with transaction support.
        /// </summary>
        /// <returns>A tuple containing the connection and transaction</returns>
        Task<(IDbConnection Connection, IDbTransaction Transaction)> CreateConnectionWithTransactionAsync();

        /// <summary>
        /// Gets the connection string for the database.
        /// </summary>
        string ConnectionString { get; }
    }
}
