// =============================================
// Lead History Migration - Error Handling Examples
// Demonstrates the comprehensive error handling implementation
// =============================================

using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Application.NewLeadHistory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LeadHistoryMigration.Examples
{
    /// <summary>
    /// Examples demonstrating the comprehensive error handling in repository operations.
    /// Shows how different error scenarios are handled with proper logging and user-friendly messages.
    /// </summary>
    public class ErrorHandlingExamples
    {
        private readonly ILeadHistoryHotRepository _hotRepository;
        private readonly ILeadHistoryRepository _repository;
        private readonly ILogger<ErrorHandlingExamples> _logger;

        public ErrorHandlingExamples(
            ILeadHistoryHotRepository hotRepository,
            ILeadHistoryRepository repository,
            ILogger<ErrorHandlingExamples> logger)
        {
            _hotRepository = hotRepository;
            _repository = repository;
            _logger = logger;
        }

        /// <summary>
        /// Example 1: Successful operation with performance logging
        /// </summary>
        public async Task<string> Example1_SuccessfulOperation()
        {
            try
            {
                _logger.LogInformation("=== Example 1: Successful Operation ===");
                
                // This will log:
                // DEBUG: Executing GetByIdAsync for LeadHistoryHot with Id: {Id}
                // DEBUG: GetByIdAsync completed successfully in {ElapsedMs}ms for Id: {Id}
                var result = await _hotRepository.GetByIdAsync(Guid.NewGuid());
                
                return "✅ Operation completed successfully with performance logging";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Example 1 failed");
                return $"❌ Example 1 failed: {ex.Message}";
            }
        }

        /// <summary>
        /// Example 2: Handling duplicate key violations during insert
        /// </summary>
        public async Task<string> Example2_DuplicateKeyHandling()
        {
            try
            {
                _logger.LogInformation("=== Example 2: Duplicate Key Handling ===");
                
                var record = new LeadHistoryHot
                {
                    Id = Guid.NewGuid(),
                    LeadId = Guid.NewGuid(),
                    FieldName = "Email",
                    FieldType = "String",
                    OldValue = "<EMAIL>",
                    NewValue = "<EMAIL>",
                    ModifiedBy = "TestUser",
                    ModifiedOn = DateTime.UtcNow,
                    LastModifiedById = Guid.NewGuid(),
                    GroupKey = Guid.NewGuid(),
                    Version = 1,
                    UserId = Guid.NewGuid(),
                    TenantId = Guid.NewGuid(),
                    IsDeleted = false
                };

                // First insert - should succeed
                await _hotRepository.InsertAsync(record);
                
                // Second insert with same ID - will trigger duplicate key handling
                // This will log:
                // WARNING: Duplicate key violation in InsertAsync for Id: {Id}
                // Exception: "A lead history hot record with ID {id} already exists"
                await _hotRepository.InsertAsync(record);
                
                return "✅ Duplicate key handled gracefully";
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("already exists"))
            {
                _logger.LogInformation("Duplicate key violation handled as expected");
                return "✅ Duplicate key violation handled correctly with user-friendly message";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Example 2 failed unexpectedly");
                return $"❌ Example 2 failed: {ex.Message}";
            }
        }

        /// <summary>
        /// Example 3: Bulk operation with transaction rollback
        /// </summary>
        public async Task<string> Example3_BulkOperationWithErrorHandling()
        {
            try
            {
                _logger.LogInformation("=== Example 3: Bulk Operation Error Handling ===");
                
                var records = new List<LeadHistoryHot>();
                for (int i = 0; i < 5; i++)
                {
                    records.Add(new LeadHistoryHot
                    {
                        Id = Guid.NewGuid(),
                        LeadId = Guid.NewGuid(),
                        FieldName = $"Field{i}",
                        FieldType = "String",
                        OldValue = $"OldValue{i}",
                        NewValue = $"NewValue{i}",
                        ModifiedBy = "BulkTestUser",
                        ModifiedOn = DateTime.UtcNow,
                        LastModifiedById = Guid.NewGuid(),
                        GroupKey = Guid.NewGuid(),
                        Version = i + 1,
                        UserId = Guid.NewGuid(),
                        TenantId = Guid.NewGuid(),
                        IsDeleted = false
                    });
                }

                // This will log:
                // DEBUG: Executing BulkInsertAsync for 5 LeadHistoryHot records
                // DEBUG: BulkInsertAsync completed successfully in {ElapsedMs}ms. Records processed: 5, Rows affected: 5
                var result = await _hotRepository.BulkInsertAsync(records);
                
                return $"✅ Bulk operation completed successfully. {result} records inserted with comprehensive logging";
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("bulk insert"))
            {
                _logger.LogInformation("Bulk operation error handled as expected");
                return "✅ Bulk operation error handled with transaction rollback and detailed logging";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Example 3 failed unexpectedly");
                return $"❌ Example 3 failed: {ex.Message}";
            }
        }

        /// <summary>
        /// Example 4: Timeout handling demonstration
        /// </summary>
        public async Task<string> Example4_TimeoutHandling()
        {
            try
            {
                _logger.LogInformation("=== Example 4: Timeout Handling ===");
                
                // Simulate a query that might timeout
                // This will log:
                // WARNING: Timeout in GetAllAsync. Operation: SELECT. ElapsedMs: {ElapsedMs}
                // Exception: "Database operation timed out while retrieving lead history records"
                var result = await _repository.GetAllAsync(pageNumber: 1, pageSize: 10000);
                
                return $"✅ Large query completed successfully. {result.Count} records retrieved";
            }
            catch (TimeoutException ex)
            {
                _logger.LogInformation("Timeout handled as expected");
                return "✅ Timeout handled gracefully with performance context and user-friendly message";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Example 4 failed unexpectedly");
                return $"❌ Example 4 failed: {ex.Message}";
            }
        }

        /// <summary>
        /// Example 5: Parameter validation and null handling
        /// </summary>
        public async Task<string> Example5_ParameterValidation()
        {
            try
            {
                _logger.LogInformation("=== Example 5: Parameter Validation ===");
                
                // This will trigger parameter validation
                // Will log: ERROR: Unexpected error in InsertAsync for Id: {null}
                // Exception: "An unexpected error occurred while inserting lead history hot record"
                await _hotRepository.InsertAsync(null);
                
                return "✅ Parameter validation handled";
            }
            catch (ArgumentNullException ex)
            {
                _logger.LogInformation("Null parameter handled as expected");
                return "✅ Null parameter validation handled correctly";
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogInformation("Parameter validation handled through InvalidOperationException");
                return "✅ Parameter validation handled with proper error wrapping";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Example 5 failed unexpectedly");
                return $"❌ Example 5 failed: {ex.Message}";
            }
        }

        /// <summary>
        /// Example 6: Performance monitoring demonstration
        /// </summary>
        public async Task<string> Example6_PerformanceMonitoring()
        {
            try
            {
                _logger.LogInformation("=== Example 6: Performance Monitoring ===");
                
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                
                // Multiple operations to demonstrate performance logging
                var tasks = new List<Task>();
                for (int i = 0; i < 3; i++)
                {
                    tasks.Add(_hotRepository.GetByIdAsync(Guid.NewGuid()));
                }
                
                await Task.WhenAll(tasks);
                stopwatch.Stop();
                
                // Each operation will log its individual performance:
                // DEBUG: GetByIdAsync completed successfully in {ElapsedMs}ms for Id: {Id}
                
                _logger.LogInformation("All operations completed in {TotalElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                
                return $"✅ Performance monitoring demonstrated. Total time: {stopwatch.ElapsedMilliseconds}ms";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Example 6 failed");
                return $"❌ Example 6 failed: {ex.Message}";
            }
        }

        /// <summary>
        /// Runs all error handling examples
        /// </summary>
        public async Task<List<string>> RunAllExamples()
        {
            var results = new List<string>();
            
            _logger.LogInformation("🚀 Starting Error Handling Examples Demonstration");
            
            results.Add(await Example1_SuccessfulOperation());
            results.Add(await Example2_DuplicateKeyHandling());
            results.Add(await Example3_BulkOperationWithErrorHandling());
            results.Add(await Example4_TimeoutHandling());
            results.Add(await Example5_ParameterValidation());
            results.Add(await Example6_PerformanceMonitoring());
            
            _logger.LogInformation("✅ All Error Handling Examples Completed");
            
            return results;
        }
    }
}

/*
Expected Log Output Examples:

=== DEBUG LOGS ===
[DEBUG] Executing GetByIdAsync for LeadHistoryHot with Id: 12345678-1234-1234-1234-123456789012
[DEBUG] GetByIdAsync completed successfully in 45ms for Id: 12345678-1234-1234-1234-123456789012
[DEBUG] Executing BulkInsertAsync for 5 LeadHistoryHot records
[DEBUG] BulkInsertAsync completed successfully in 150ms. Records processed: 5, Rows affected: 5

=== WARNING LOGS ===
[WARN] Duplicate key violation in InsertAsync for Id: 12345678-1234-1234-1234-123456789012. Operation: INSERT. ElapsedMs: 25
[WARN] Timeout in GetAllAsync with PageNumber: 1, PageSize: 10000. Operation: SELECT. ElapsedMs: 30000

=== ERROR LOGS ===
[ERROR] Database error in BulkInsertAsync. Operation: BULK INSERT. RecordCount: 5. ElapsedMs: 2500. Error: Connection timeout
[ERROR] Unexpected error in InsertAsync for Id: (null). Operation: INSERT. ElapsedMs: 5. Error: Value cannot be null

=== USER-FRIENDLY EXCEPTION MESSAGES ===
- "A lead history hot record with ID 12345678-1234-1234-1234-123456789012 already exists"
- "Failed to bulk insert 5 lead history hot records"
- "Database operation timed out while retrieving lead history records (page 1, size 10000)"
- "An unexpected error occurred while inserting lead history hot record with ID 12345678-1234-1234-1234-123456789012"
*/
