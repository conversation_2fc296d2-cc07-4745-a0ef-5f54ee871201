﻿using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Infra.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LeadHistoryMigration
{
    /// <summary>
    /// Main application class demonstrating the Dapper-based lead history transformation and persistence.
    /// </summary>
    public class App
    {
        private readonly ILeadHistoryRepository _leadHistoryRepository;
        private readonly ILeadHistoryTransformer _leadHistoryTransformer;
        private readonly ILeadHistoryHotRepository _hotRepository;
        private readonly ApplicationDbContext _dbContext;

        public App(
            ILeadHistoryRepository leadHistoryRepository,
            ILeadHistoryTransformer leadHistoryTransformer,
            ILeadHistoryHotRepository hotRepository,
            ApplicationDbContext dbContext)
        {
            _leadHistoryRepository = leadHistoryRepository;
            _leadHistoryTransformer = leadHistoryTransformer;
            _hotRepository = hotRepository;
            _dbContext = dbContext;
        }

        public async Task RunAsync()
        {
            Console.WriteLine("=== Lead History Migration Application ===");
            Console.WriteLine("Using Dapper for high-performance data access");
            Console.WriteLine();

            try
            {
                // Demonstrate reading from source using Dapper
                Console.WriteLine("1. Fetching lead history data using Dapper repository...");
                var leadHistories = await _leadHistoryRepository.GetAllAsync(pageNumber: 1, pageSize: 10);
                Console.WriteLine($"   Retrieved {leadHistories.Count} lead history records");

                if (leadHistories.Count == 0)
                {
                    Console.WriteLine("   No lead history records found. Creating sample data would be needed for full demonstration.");
                    Console.WriteLine("Application completed successfully.");
                    return;
                }

                // Demonstrate transformation only
                Console.WriteLine("\n2. Transforming data to field-wise format...");
                var transformedData = await _leadHistoryTransformer.TransformMultipleToFieldWiseAsync(leadHistories);
                Console.WriteLine($"   Generated {transformedData.Count} field-wise change records");

                // Demonstrate transformation with persistence
                Console.WriteLine("\n3. Demonstrating transform and persist functionality...");
                if (leadHistories.Count > 0)
                {
                    var firstLead = leadHistories.First();
                    var persistedCount = await _leadHistoryTransformer.TransformAndPersistAsync(firstLead);
                    Console.WriteLine($"   Transformed and persisted {persistedCount} records for lead {firstLead.Id}");
                }

                // Show repository statistics
                Console.WriteLine("\n4. Repository statistics:");
                var totalHotRecords = await _hotRepository.GetCountAsync();
                Console.WriteLine($"   Total records in hot storage: {totalHotRecords}");

                // Demonstrate bulk operations
                if (leadHistories.Count > 1)
                {
                    Console.WriteLine("\n5. Demonstrating bulk transform and persist...");
                    var bulkLeads = leadHistories.Take(3).ToList();
                    var bulkPersistedCount = await _leadHistoryTransformer.TransformAndPersistMultipleAsync(bulkLeads);
                    Console.WriteLine($"   Bulk processed {bulkPersistedCount} records from {bulkLeads.Count} lead histories");
                }

                Console.WriteLine("\n=== Application completed successfully ===");
                Console.WriteLine("All operations used Dapper for optimal performance!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\nError: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
