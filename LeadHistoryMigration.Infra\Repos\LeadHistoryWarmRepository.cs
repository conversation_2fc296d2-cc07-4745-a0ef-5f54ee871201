using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Infra.Database;
using Dapper;
using System.Data;
using Microsoft.Extensions.Logging;
using Npgsql;
using System.Diagnostics;

namespace LeadHistoryMigration.Infra.Repos
{
    /// <summary>
    /// Dapper-based repository for LeadHistoryWarm entities (intermediate storage).
    /// Optimized for moderate-frequency access patterns with comprehensive error handling.
    /// </summary>
    public class LeadHistoryWarmRepository : ILeadHistoryWarmRepository
    {
        private readonly IDapperConnectionFactory _connectionFactory;
        private readonly ILogger<LeadHistoryWarmRepository> _logger;

        public LeadHistoryWarmRepository(IDapperConnectionFactory connectionFactory, ILogger<LeadHistoryWarmRepository> logger)
        {
            _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a lead history warm record by its ID.
        /// </summary>
        public async Task<LeadHistoryWarm?> GetByIdAsync(Guid id)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryWarm""
                WHERE ""Id"" = @Id AND ""IsDeleted"" = false";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.QueryFirstOrDefaultAsync<LeadHistoryWarm>(sql, new { Id = id });
        }

        /// <summary>
        /// Gets all lead history warm records for a specific lead ID with pagination.
        /// </summary>
        public async Task<List<LeadHistoryWarm>> GetByLeadIdAsync(Guid leadId, int pageNumber = 1, int pageSize = 100)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryWarm""
                WHERE ""LeadId"" = @LeadId AND ""IsDeleted"" = false
                ORDER BY ""ModifiedOn"" DESC, ""Version"" DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            var offset = (pageNumber - 1) * pageSize;
            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            var result = await connection.QueryAsync<LeadHistoryWarm>(sql, new { LeadId = leadId, Offset = offset, PageSize = pageSize });
            return result.ToList();
        }

        /// <summary>
        /// Gets lead history warm records by group key.
        /// </summary>
        public async Task<List<LeadHistoryWarm>> GetByGroupKeyAsync(Guid groupKey)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryWarm""
                WHERE ""GroupKey"" = @GroupKey AND ""IsDeleted"" = false
                ORDER BY ""LeadId"", ""FieldName"", ""Version""";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            var result = await connection.QueryAsync<LeadHistoryWarm>(sql, new { GroupKey = groupKey });
            return result.ToList();
        }

        /// <summary>
        /// Gets lead history warm records within a date range with pagination.
        /// </summary>
        public async Task<List<LeadHistoryWarm>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int pageNumber = 1, int pageSize = 100)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryWarm""
                WHERE ""ModifiedOn"" >= @StartDate AND ""ModifiedOn"" <= @EndDate AND ""IsDeleted"" = false
                ORDER BY ""ModifiedOn"" DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            var offset = (pageNumber - 1) * pageSize;
            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            var result = await connection.QueryAsync<LeadHistoryWarm>(sql, new { StartDate = startDate, EndDate = endDate, Offset = offset, PageSize = pageSize });
            return result.ToList();
        }

        /// <summary>
        /// Gets the total count of lead history warm records.
        /// </summary>
        public async Task<int> GetCountAsync()
        {
            const string sql = @"SELECT COUNT(*) FROM ""LeadratBlack"".""LeadHistoryWarm"" WHERE ""IsDeleted"" = false";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.QuerySingleAsync<int>(sql);
        }

        /// <summary>
        /// Inserts a single lead history warm record.
        /// </summary>
        public async Task<int> InsertAsync(LeadHistoryWarm record)
        {
            const string sql = @"
                INSERT INTO ""LeadratBlack"".""LeadHistoryWarm""
                (""Id"", ""LeadId"", ""FieldName"", ""FieldType"", ""OldValue"", ""NewValue"", ""ModifiedBy"",
                 ""ModifiedOn"", ""LastModifiedById"", ""GroupKey"", ""Version"", ""UserId"", ""TenantId"", ""IsDeleted"")
                VALUES
                (@Id, @LeadId, @FieldName, @FieldType, @OldValue, @NewValue, @ModifiedBy,
                 @ModifiedOn, @LastModifiedById, @GroupKey, @Version, @UserId, @TenantId, @IsDeleted)";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, record);
        }

        /// <summary>
        /// Inserts multiple lead history warm records using bulk operations.
        /// </summary>
        public async Task<int> BulkInsertAsync(IEnumerable<LeadHistoryWarm> records)
        {
            if (records == null || !records.Any())
                return 0;

            const string sql = @"
                INSERT INTO ""LeadratBlack"".""LeadHistoryWarm""
                (""Id"", ""LeadId"", ""FieldName"", ""FieldType"", ""OldValue"", ""NewValue"", ""ModifiedBy"",
                 ""ModifiedOn"", ""LastModifiedById"", ""GroupKey"", ""Version"", ""UserId"", ""TenantId"", ""IsDeleted"")
                VALUES
                (@Id, @LeadId, @FieldName, @FieldType, @OldValue, @NewValue, @ModifiedBy,
                 @ModifiedOn, @LastModifiedById, @GroupKey, @Version, @UserId, @TenantId, @IsDeleted)";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            using var transaction = connection.BeginTransaction();
            try
            {
                var result = await connection.ExecuteAsync(sql, records, transaction);
                transaction.Commit();
                return result;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// Updates a lead history warm record.
        /// </summary>
        public async Task<int> UpdateAsync(LeadHistoryWarm record)
        {
            const string sql = @"
                UPDATE ""LeadratBlack"".""LeadHistoryWarm""
                SET ""FieldName"" = @FieldName, ""FieldType"" = @FieldType, ""OldValue"" = @OldValue,
                    ""NewValue"" = @NewValue, ""ModifiedBy"" = @ModifiedBy, ""ModifiedOn"" = @ModifiedOn,
                    ""LastModifiedById"" = @LastModifiedById, ""Version"" = @Version, ""UserId"" = @UserId,
                    ""TenantId"" = @TenantId, ""IsDeleted"" = @IsDeleted
                WHERE ""Id"" = @Id";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, record);
        }

        /// <summary>
        /// Soft deletes a lead history warm record.
        /// </summary>
        public async Task<int> SoftDeleteAsync(Guid id)
        {
            const string sql = @"UPDATE ""LeadratBlack"".""LeadHistoryWarm"" SET ""IsDeleted"" = true WHERE ""Id"" = @Id";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, new { Id = id });
        }

        /// <summary>
        /// Hard deletes a lead history warm record.
        /// </summary>
        public async Task<int> DeleteAsync(Guid id)
        {
            const string sql = @"DELETE FROM ""LeadratBlack"".""LeadHistoryWarm"" WHERE ""Id"" = @Id";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, new { Id = id });
        }

        /// <summary>
        /// Archives old records by moving them to cold storage.
        /// </summary>
        public async Task<int> ArchiveOldRecordsAsync(DateTime cutoffDate)
        {
            const string sql = @"
                UPDATE ""LeadratBlack"".""LeadHistoryWarm""
                SET ""IsDeleted"" = true
                WHERE ""ModifiedOn"" < @CutoffDate AND ""IsDeleted"" = false";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, new { CutoffDate = cutoffDate });
        }
    }
}
