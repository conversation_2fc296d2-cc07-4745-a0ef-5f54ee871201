-- =============================================
-- Sample Queries for Lead History Migration
-- Common query patterns and performance examples
-- =============================================

-- =============================================
-- 1. Basic Data Retrieval Queries
-- =============================================

-- Get all changes for a specific lead (across all storage tiers)
WITH all_changes AS (
    SELECT 'Hot' as storage_tier, * FROM "LeadHistoryHot" WHERE "LeadId" = 'your-lead-id-here' AND "IsDeleted" = FALSE
    UNION ALL
    SELECT 'Warm' as storage_tier, * FROM "LeadHistoryWarm" WHERE "LeadId" = 'your-lead-id-here' AND "IsDeleted" = FALSE
    UNION ALL
    SELECT 'Cold' as storage_tier, * FROM "LeadHistoryCold" WHERE "LeadId" = 'your-lead-id-here' AND "IsDeleted" = FALSE
)
SELECT * FROM all_changes ORDER BY "ModifiedOn" DESC, "Version" DESC;

-- Get recent changes for a lead (Hot storage only)
SELECT 
    "FieldName",
    "OldValue",
    "NewValue",
    "ModifiedBy",
    "ModifiedOn",
    "Version"
FROM "LeadHistoryHot"
WHERE "LeadId" = 'your-lead-id-here' 
    AND "IsDeleted" = FALSE
    AND "ModifiedOn" >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY "ModifiedOn" DESC, "Version" DESC;

-- Get changes for a specific field across all leads
SELECT 
    "LeadId",
    "OldValue",
    "NewValue",
    "ModifiedBy",
    "ModifiedOn"
FROM "LeadHistoryHot"
WHERE "FieldName" = 'Email' 
    AND "IsDeleted" = FALSE
    AND "ModifiedOn" >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY "ModifiedOn" DESC;

-- =============================================
-- 2. Analytical Queries
-- =============================================

-- Most frequently changed fields
SELECT 
    "FieldName",
    COUNT(*) as change_count,
    COUNT(DISTINCT "LeadId") as affected_leads,
    MIN("ModifiedOn") as first_change,
    MAX("ModifiedOn") as last_change
FROM "LeadHistoryHot"
WHERE "IsDeleted" = FALSE
    AND "ModifiedOn" >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY "FieldName"
ORDER BY change_count DESC;

-- Most active users (by number of changes made)
SELECT 
    "ModifiedBy",
    COUNT(*) as changes_made,
    COUNT(DISTINCT "LeadId") as leads_modified,
    COUNT(DISTINCT "FieldName") as fields_modified,
    MIN("ModifiedOn") as first_activity,
    MAX("ModifiedOn") as last_activity
FROM "LeadHistoryHot"
WHERE "IsDeleted" = FALSE
    AND "ModifiedOn" >= CURRENT_DATE - INTERVAL '30 days'
    AND "ModifiedBy" IS NOT NULL
GROUP BY "ModifiedBy"
ORDER BY changes_made DESC
LIMIT 20;

-- Daily change volume
SELECT 
    DATE("ModifiedOn") as change_date,
    COUNT(*) as total_changes,
    COUNT(DISTINCT "LeadId") as leads_affected,
    COUNT(DISTINCT "ModifiedBy") as active_users
FROM "LeadHistoryHot"
WHERE "IsDeleted" = FALSE
    AND "ModifiedOn" >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE("ModifiedOn")
ORDER BY change_date DESC;

-- =============================================
-- 3. Data Quality and Audit Queries
-- =============================================

-- Find records with missing or invalid data
SELECT 
    'Missing ModifiedBy' as issue_type,
    COUNT(*) as record_count
FROM "LeadHistoryHot"
WHERE "ModifiedBy" IS NULL OR "ModifiedBy" = ''
    AND "IsDeleted" = FALSE

UNION ALL

SELECT 
    'Missing ModifiedOn' as issue_type,
    COUNT(*) as record_count
FROM "LeadHistoryHot"
WHERE "ModifiedOn" IS NULL
    AND "IsDeleted" = FALSE

UNION ALL

SELECT 
    'Same Old/New Value' as issue_type,
    COUNT(*) as record_count
FROM "LeadHistoryHot"
WHERE "OldValue" = "NewValue"
    AND "IsDeleted" = FALSE

UNION ALL

SELECT 
    'Missing Field Name' as issue_type,
    COUNT(*) as record_count
FROM "LeadHistoryHot"
WHERE "FieldName" IS NULL OR "FieldName" = ''
    AND "IsDeleted" = FALSE;

-- Find duplicate records (same lead, field, version)
SELECT 
    "LeadId",
    "FieldName",
    "Version",
    COUNT(*) as duplicate_count
FROM "LeadHistoryHot"
WHERE "IsDeleted" = FALSE
GROUP BY "LeadId", "FieldName", "Version"
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- =============================================
-- 4. Performance Monitoring Queries
-- =============================================

-- Table sizes and record counts
SELECT 
    'LeadHistoryHot' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE "IsDeleted" = FALSE) as active_records,
    pg_size_pretty(pg_total_relation_size('"LeadHistoryHot"')) as table_size
FROM "LeadHistoryHot"

UNION ALL

SELECT 
    'LeadHistoryWarm' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE "IsDeleted" = FALSE) as active_records,
    pg_size_pretty(pg_total_relation_size('"LeadHistoryWarm"')) as table_size
FROM "LeadHistoryWarm"

UNION ALL

SELECT 
    'LeadHistoryCold' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE "IsDeleted" = FALSE) as active_records,
    pg_size_pretty(pg_total_relation_size('"LeadHistoryCold"')) as table_size
FROM "LeadHistoryCold";

-- Index usage statistics
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes
WHERE tablename IN ('LeadHistoryHot', 'LeadHistoryWarm', 'LeadHistoryCold')
ORDER BY tablename, idx_scan DESC;

-- =============================================
-- 5. Maintenance and Archival Queries
-- =============================================

-- Check records eligible for archival (Hot to Warm)
SELECT 
    COUNT(*) as records_to_archive,
    MIN("ModifiedOn") as oldest_record,
    MAX("ModifiedOn") as newest_eligible
FROM "LeadHistoryHot"
WHERE "ModifiedOn" < CURRENT_DATE - INTERVAL '90 days'
    AND "IsDeleted" = FALSE;

-- Check records eligible for archival (Warm to Cold)
SELECT 
    COUNT(*) as records_to_archive,
    MIN("ModifiedOn") as oldest_record,
    MAX("ModifiedOn") as newest_eligible
FROM "LeadHistoryWarm"
WHERE "ModifiedOn" < CURRENT_DATE - INTERVAL '1 year'
    AND "IsDeleted" = FALSE;

-- Check records eligible for purging (Cold storage)
SELECT 
    COUNT(*) as records_to_purge,
    MIN("ModifiedOn") as oldest_record,
    MAX("ModifiedOn") as newest_eligible,
    pg_size_pretty(pg_total_relation_size('"LeadHistoryCold"')) as current_cold_size
FROM "LeadHistoryCold"
WHERE "ModifiedOn" < CURRENT_DATE - INTERVAL '7 years';

-- =============================================
-- 6. Example Archival Operations
-- =============================================

-- Archive Hot to Warm (records older than 90 days)
-- SELECT * FROM archive_hot_to_warm(CURRENT_DATE - INTERVAL '90 days', 1000);

-- Archive Warm to Cold (records older than 1 year)
-- SELECT * FROM archive_warm_to_cold(CURRENT_DATE - INTERVAL '1 year', 1000);

-- Purge Cold storage (records older than 7 years)
-- SELECT * FROM purge_cold_storage(CURRENT_DATE - INTERVAL '7 years', 1000);

-- Get current storage statistics
-- SELECT * FROM get_storage_statistics();

-- =============================================
-- 7. Backup and Recovery Queries
-- =============================================

-- Export data for a specific lead (for backup or migration)
COPY (
    SELECT 'Hot' as source_table, * FROM "LeadHistoryHot" 
    WHERE "LeadId" = 'your-lead-id-here' AND "IsDeleted" = FALSE
    UNION ALL
    SELECT 'Warm' as source_table, * FROM "LeadHistoryWarm" 
    WHERE "LeadId" = 'your-lead-id-here' AND "IsDeleted" = FALSE
    UNION ALL
    SELECT 'Cold' as source_table, * FROM "LeadHistoryCold" 
    WHERE "LeadId" = 'your-lead-id-here' AND "IsDeleted" = FALSE
    ORDER BY "ModifiedOn" DESC
) TO '/tmp/lead_history_backup.csv' WITH CSV HEADER;

-- Verify data integrity after migration
SELECT 
    h."LeadId",
    COUNT(h.*) as hot_records,
    COUNT(w.*) as warm_records,
    COUNT(c.*) as cold_records,
    COUNT(h.*) + COUNT(w.*) + COUNT(c.*) as total_records
FROM "LeadHistoryHot" h
FULL OUTER JOIN "LeadHistoryWarm" w ON h."LeadId" = w."LeadId"
FULL OUTER JOIN "LeadHistoryCold" c ON COALESCE(h."LeadId", w."LeadId") = c."LeadId"
WHERE COALESCE(h."IsDeleted", w."IsDeleted", c."IsDeleted") = FALSE
GROUP BY h."LeadId"
HAVING COUNT(h.*) + COUNT(w.*) + COUNT(c.*) > 0
ORDER BY total_records DESC
LIMIT 100;
