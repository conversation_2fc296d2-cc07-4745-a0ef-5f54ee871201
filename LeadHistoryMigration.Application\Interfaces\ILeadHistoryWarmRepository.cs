using LeadHistoryMigration.Application.NewLeadHistory;

namespace LeadHistoryMigration.Application.Interfaces
{
    /// <summary>
    /// Repository interface for LeadHistoryWarm entities (intermediate storage).
    /// Provides data access for moderately active lead history records.
    /// </summary>
    public interface ILeadHistoryWarmRepository
    {
        /// <summary>
        /// Gets a lead history warm record by its ID.
        /// </summary>
        Task<LeadHistoryWarm?> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets all lead history warm records for a specific lead ID.
        /// </summary>
        Task<List<LeadHistoryWarm>> GetByLeadIdAsync(Guid leadId, int pageNumber = 1, int pageSize = 100);

        /// <summary>
        /// Gets lead history warm records by group key.
        /// </summary>
        Task<List<LeadHistoryWarm>> GetByGroupKeyAsync(Guid groupKey);

        /// <summary>
        /// Gets lead history warm records within a date range.
        /// </summary>
        Task<List<LeadHistoryWarm>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int pageNumber = 1, int pageSize = 100);

        /// <summary>
        /// Gets the total count of lead history warm records.
        /// </summary>
        Task<int> GetCountAsync();

        /// <summary>
        /// Inserts a single lead history warm record.
        /// </summary>
        Task<int> InsertAsync(LeadHistoryWarm record);

        /// <summary>
        /// Inserts multiple lead history warm records using bulk operations.
        /// </summary>
        Task<int> BulkInsertAsync(IEnumerable<LeadHistoryWarm> records);

        /// <summary>
        /// Updates a lead history warm record.
        /// </summary>
        Task<int> UpdateAsync(LeadHistoryWarm record);

        /// <summary>
        /// Soft deletes a lead history warm record.
        /// </summary>
        Task<int> SoftDeleteAsync(Guid id);

        /// <summary>
        /// Hard deletes a lead history warm record.
        /// </summary>
        Task<int> DeleteAsync(Guid id);

        /// <summary>
        /// Archives old records by moving them to cold storage.
        /// </summary>
        Task<int> ArchiveOldRecordsAsync(DateTime cutoffDate);
    }
}
