-- =============================================
-- Stored Procedures for Lead History Migration
-- High-performance procedures for data archival and maintenance
-- =============================================

-- =============================================
-- Procedure: Archive Hot to Warm Storage
-- Moves old records from Hot to Warm storage based on age
-- =============================================
CREATE OR REPLACE FUNCTION archive_hot_to_warm(
    p_cutoff_date TIMESTAMP WITH TIME ZONE,
    p_batch_size INTEGER DEFAULT 1000
)
RETURNS TABLE(
    records_moved INTEGER,
    execution_time_ms INTEGER
) AS $$
DECLARE
    v_start_time TIMESTAMP;
    v_records_moved INTEGER := 0;
    v_batch_count INTEGER;
    v_total_batches INTEGER;
BEGIN
    v_start_time := clock_timestamp();
    
    -- Get total count for progress tracking
    SELECT COUNT(*) INTO v_total_batches
    FROM "LeadHistoryHot"
    WHERE "ModifiedOn" < p_cutoff_date AND "IsDeleted" = FALSE;
    
    v_total_batches := CEIL(v_total_batches::DECIMAL / p_batch_size);
    
    -- Process in batches to avoid long-running transactions
    FOR v_batch_count IN 1..v_total_batches LOOP
        -- Insert batch into warm storage
        WITH hot_batch AS (
            SELECT *
            FROM "LeadHistoryHot"
            WHERE "ModifiedOn" < p_cutoff_date AND "IsDeleted" = FALSE
            ORDER BY "ModifiedOn"
            LIMIT p_batch_size
        )
        INSERT INTO "LeadHistoryWarm" (
            "Id", "LeadId", "FieldName", "FieldType", "OldValue", "NewValue",
            "ModifiedBy", "ModifiedOn", "LastModifiedById", "GroupKey",
            "Version", "UserId", "TenantId", "IsDeleted", "CreatedAt"
        )
        SELECT 
            "Id", "LeadId", "FieldName", "FieldType", "OldValue", "NewValue",
            "ModifiedBy", "ModifiedOn", "LastModifiedById", "GroupKey",
            "Version", "UserId", "TenantId", "IsDeleted", "CreatedAt"
        FROM hot_batch;
        
        -- Get count of inserted records
        GET DIAGNOSTICS v_records_moved = ROW_COUNT;
        
        -- Delete from hot storage
        WITH hot_batch AS (
            SELECT "Id"
            FROM "LeadHistoryHot"
            WHERE "ModifiedOn" < p_cutoff_date AND "IsDeleted" = FALSE
            ORDER BY "ModifiedOn"
            LIMIT p_batch_size
        )
        DELETE FROM "LeadHistoryHot"
        WHERE "Id" IN (SELECT "Id" FROM hot_batch);
        
        -- Exit if no more records to process
        EXIT WHEN v_records_moved = 0;
        
        -- Commit batch (if in a transaction block, this will be a savepoint)
        COMMIT;
    END LOOP;
    
    -- Return results
    RETURN QUERY SELECT 
        v_records_moved,
        EXTRACT(MILLISECONDS FROM clock_timestamp() - v_start_time)::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- Procedure: Archive Warm to Cold Storage
-- Moves old records from Warm to Cold storage based on age
-- =============================================
CREATE OR REPLACE FUNCTION archive_warm_to_cold(
    p_cutoff_date TIMESTAMP WITH TIME ZONE,
    p_batch_size INTEGER DEFAULT 1000
)
RETURNS TABLE(
    records_moved INTEGER,
    execution_time_ms INTEGER
) AS $$
DECLARE
    v_start_time TIMESTAMP;
    v_records_moved INTEGER := 0;
    v_batch_count INTEGER;
    v_total_batches INTEGER;
BEGIN
    v_start_time := clock_timestamp();
    
    -- Get total count for progress tracking
    SELECT COUNT(*) INTO v_total_batches
    FROM "LeadHistoryWarm"
    WHERE "ModifiedOn" < p_cutoff_date AND "IsDeleted" = FALSE;
    
    v_total_batches := CEIL(v_total_batches::DECIMAL / p_batch_size);
    
    -- Process in batches
    FOR v_batch_count IN 1..v_total_batches LOOP
        -- Insert batch into cold storage
        WITH warm_batch AS (
            SELECT *
            FROM "LeadHistoryWarm"
            WHERE "ModifiedOn" < p_cutoff_date AND "IsDeleted" = FALSE
            ORDER BY "ModifiedOn"
            LIMIT p_batch_size
        )
        INSERT INTO "LeadHistoryCold" (
            "Id", "LeadId", "FieldName", "FieldType", "OldValue", "NewValue",
            "ModifiedBy", "ModifiedOn", "LastModifiedById", "GroupKey",
            "Version", "UserId", "TenantId", "IsDeleted", "CreatedAt"
        )
        SELECT 
            "Id", "LeadId", "FieldName", "FieldType", "OldValue", "NewValue",
            "ModifiedBy", "ModifiedOn", "LastModifiedById", "GroupKey",
            "Version", "UserId", "TenantId", "IsDeleted", "CreatedAt"
        FROM warm_batch;
        
        -- Get count of inserted records
        GET DIAGNOSTICS v_records_moved = ROW_COUNT;
        
        -- Delete from warm storage
        WITH warm_batch AS (
            SELECT "Id"
            FROM "LeadHistoryWarm"
            WHERE "ModifiedOn" < p_cutoff_date AND "IsDeleted" = FALSE
            ORDER BY "ModifiedOn"
            LIMIT p_batch_size
        )
        DELETE FROM "LeadHistoryWarm"
        WHERE "Id" IN (SELECT "Id" FROM warm_batch);
        
        -- Exit if no more records to process
        EXIT WHEN v_records_moved = 0;
        
        -- Commit batch
        COMMIT;
    END LOOP;
    
    -- Return results
    RETURN QUERY SELECT 
        v_records_moved,
        EXTRACT(MILLISECONDS FROM clock_timestamp() - v_start_time)::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- Procedure: Purge Old Cold Storage Records
-- Permanently removes very old records from cold storage
-- =============================================
CREATE OR REPLACE FUNCTION purge_cold_storage(
    p_cutoff_date TIMESTAMP WITH TIME ZONE,
    p_batch_size INTEGER DEFAULT 1000
)
RETURNS TABLE(
    records_purged INTEGER,
    execution_time_ms INTEGER
) AS $$
DECLARE
    v_start_time TIMESTAMP;
    v_records_purged INTEGER := 0;
    v_batch_records INTEGER;
BEGIN
    v_start_time := clock_timestamp();
    
    -- Process in batches to avoid long-running transactions
    LOOP
        -- Delete batch
        DELETE FROM "LeadHistoryCold"
        WHERE "Id" IN (
            SELECT "Id"
            FROM "LeadHistoryCold"
            WHERE "ModifiedOn" < p_cutoff_date
            ORDER BY "ModifiedOn"
            LIMIT p_batch_size
        );
        
        -- Get count of deleted records
        GET DIAGNOSTICS v_batch_records = ROW_COUNT;
        v_records_purged := v_records_purged + v_batch_records;
        
        -- Exit if no more records to process
        EXIT WHEN v_batch_records = 0;
        
        -- Commit batch
        COMMIT;
    END LOOP;
    
    -- Return results
    RETURN QUERY SELECT 
        v_records_purged,
        EXTRACT(MILLISECONDS FROM clock_timestamp() - v_start_time)::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- Procedure: Get Storage Statistics
-- Returns statistics about record distribution across storage tiers
-- =============================================
CREATE OR REPLACE FUNCTION get_storage_statistics()
RETURNS TABLE(
    storage_tier VARCHAR(10),
    total_records BIGINT,
    active_records BIGINT,
    deleted_records BIGINT,
    oldest_record TIMESTAMP WITH TIME ZONE,
    newest_record TIMESTAMP WITH TIME ZONE,
    avg_records_per_lead DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'Hot'::VARCHAR(10) as storage_tier,
        COUNT(*) as total_records,
        COUNT(*) FILTER (WHERE "IsDeleted" = FALSE) as active_records,
        COUNT(*) FILTER (WHERE "IsDeleted" = TRUE) as deleted_records,
        MIN("ModifiedOn") as oldest_record,
        MAX("ModifiedOn") as newest_record,
        ROUND(COUNT(*)::DECIMAL / NULLIF(COUNT(DISTINCT "LeadId"), 0), 2) as avg_records_per_lead
    FROM "LeadHistoryHot"
    
    UNION ALL
    
    SELECT 
        'Warm'::VARCHAR(10) as storage_tier,
        COUNT(*) as total_records,
        COUNT(*) FILTER (WHERE "IsDeleted" = FALSE) as active_records,
        COUNT(*) FILTER (WHERE "IsDeleted" = TRUE) as deleted_records,
        MIN("ModifiedOn") as oldest_record,
        MAX("ModifiedOn") as newest_record,
        ROUND(COUNT(*)::DECIMAL / NULLIF(COUNT(DISTINCT "LeadId"), 0), 2) as avg_records_per_lead
    FROM "LeadHistoryWarm"
    
    UNION ALL
    
    SELECT 
        'Cold'::VARCHAR(10) as storage_tier,
        COUNT(*) as total_records,
        COUNT(*) FILTER (WHERE "IsDeleted" = FALSE) as active_records,
        COUNT(*) FILTER (WHERE "IsDeleted" = TRUE) as deleted_records,
        MIN("ModifiedOn") as oldest_record,
        MAX("ModifiedOn") as newest_record,
        ROUND(COUNT(*)::DECIMAL / NULLIF(COUNT(DISTINCT "LeadId"), 0), 2) as avg_records_per_lead
    FROM "LeadHistoryCold"
    
    ORDER BY 
        CASE storage_tier 
            WHEN 'Hot' THEN 1 
            WHEN 'Warm' THEN 2 
            WHEN 'Cold' THEN 3 
        END;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- Comments for documentation
-- =============================================

COMMENT ON FUNCTION archive_hot_to_warm IS 'Archives old records from Hot to Warm storage with batch processing for performance';
COMMENT ON FUNCTION archive_warm_to_cold IS 'Archives old records from Warm to Cold storage with batch processing for performance';
COMMENT ON FUNCTION purge_cold_storage IS 'Permanently removes very old records from Cold storage to manage storage costs';
COMMENT ON FUNCTION get_storage_statistics IS 'Returns comprehensive statistics about record distribution across all storage tiers';
