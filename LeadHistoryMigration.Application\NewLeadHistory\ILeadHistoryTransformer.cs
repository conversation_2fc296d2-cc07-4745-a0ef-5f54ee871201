﻿using LeadHistoryMigration.Application.ExistingHistory;

namespace LeadHistoryMigration.Application.NewLeadHistory
{
    /// <summary>
    /// Interface for transforming lead history data from versioned dictionary format to field-wise change records.
    /// Includes both transformation and persistence capabilities.
    /// </summary>
    public interface ILeadHistoryTransformer
    {
        /// <summary>
        /// Transforms a single lead history record to field-wise change records.
        /// </summary>
        List<LeadHistoryHot> TransformToFieldWise(LeadHistory leadHistory);

        /// <summary>
        /// Transforms multiple lead history records to field-wise change records asynchronously.
        /// </summary>
        Task<List<LeadHistoryHot>> TransformMultipleToFieldWiseAsync(List<LeadHistory> leadHistories);

        /// <summary>
        /// Transforms and persists a single lead history record to the database.
        /// </summary>
        Task<int> TransformAndPersistAsync(LeadHistory leadHistory);

        /// <summary>
        /// Transforms and persists multiple lead history records to the database using bulk operations.
        /// </summary>
        Task<int> TransformAndPersistMultipleAsync(List<LeadHistory> leadHistories);

        /// <summary>
        /// Transforms and persists with transaction support for data consistency.
        /// </summary>
        Task<int> TransformAndPersistWithTransactionAsync(List<LeadHistory> leadHistories);
    }
}
