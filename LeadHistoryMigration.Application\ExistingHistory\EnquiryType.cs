﻿using System.ComponentModel;

namespace LeadHistoryMigration.Application.ExistingHistory
{
    #region Enquiry Type
    public enum EnquiryType
    {
        [Description("None")]
        None = 0,
        [Description("Buy")]
        Buy,
        [Description("Sale")]
        Sale,
        [Description("Rent")]
        Rent
    }
    #endregion

    #region Sale Type
    public enum SaleType
    {
        [Description("None")]
        None = 0,
        [Description("New")]
        New,
        [Description("Resale")]
        Resale
    }
    #endregion

    #region Lead Source
    public enum LeadSource
    {
        Any = -1,
        [Description(EnumDescription.LeadSource.Direct)]
        Direct = 0,
        [Description(EnumDescription.LeadSource.IVR)]
        IVR,
        [Description(EnumDescription.LeadSource.Facebook)]
        Facebook,
        [Description(EnumDescription.LeadSource.LinkedIn)]
        LinkedIn,
        [Description(EnumDescription.LeadSource.GoogleAds)]
        GoogleAds,
        [Description(EnumDescription.LeadSource.MagicBricks)]
        MagicBricks,
        [Description(EnumDescription.LeadSource.NinetyNineAcres)]
        NinetyNineAcres,
        [Description(EnumDescription.LeadSource.Housing)]
        Housing,
        [Description(EnumDescription.LeadSource.GharOffice)]
        GharOffice,
        [Description(EnumDescription.LeadSource.Referral)]
        Referral,
        [Description(EnumDescription.LeadSource.WalkIn)]
        WalkIn,
        [Description(EnumDescription.LeadSource.Website)]
        Website,
        [Description(EnumDescription.LeadSource.Gmail)]
        Gmail,
        [Description(EnumDescription.LeadSource.PropertyMicrosite)]
        PropertyMicrosite,
        [Description(EnumDescription.LeadSource.PortfolioMicrosite)]
        PortfolioMicrosite,
        [Description(EnumDescription.LeadSource.Phonebook)]
        Phonebook,
        [Description(EnumDescription.LeadSource.CallLogs)]
        CallLogs,
        [Description(EnumDescription.LeadSource.LeadPool)]
        LeadPool,
        [Description(EnumDescription.LeadSource.SquareYards)]
        SquareYards,
        [Description(EnumDescription.LeadSource.QuikrHomes)]
        QuikrHomes,
        [Description(EnumDescription.LeadSource.JustLead)]
        JustLead,
        [Description(EnumDescription.LeadSource.WhatsApp)]
        WhatsApp,
        [Description(EnumDescription.LeadSource.YouTube)]
        YouTube,
        [Description(EnumDescription.LeadSource.QRCode)]
        QRCode,
        [Description(EnumDescription.LeadSource.Instagram)]
        Instagram,
        [Description(EnumDescription.LeadSource.OLX)]
        OLX,
        [Description(EnumDescription.LeadSource.EstateDekho)]
        EstateDekho,
        [Description(EnumDescription.LeadSource.GoogleSheet)]
        GoogleSheet,
        [Description(EnumDescription.LeadSource.ChannelPartner)]
        ChannelPartner,
        [Description(EnumDescription.LeadSource.RealEstateIndia)]
        RealEstateIndia,
        [Description(EnumDescription.LeadSource.CommonFloor)]
        CommonFloor,
        [Description(EnumDescription.LeadSource.Data)]
        Data,
        [Description(EnumDescription.LeadSource.RoofandFloor)]
        RoofandFloor,
        [Description(EnumDescription.LeadSource.MicrosoftAds)]
        MicrosoftAds,
        [Description(EnumDescription.LeadSource.PropertyWala)]
        PropertyWala,
        [Description(EnumDescription.LeadSource.ProjectMicrosite)]
        ProjectMicrosite,
        [Description(EnumDescription.LeadSource.MyGate)]
        MyGate,
        [Description(EnumDescription.LeadSource.Flipkart)]
        Flipkart,
        [Description(EnumDescription.LeadSource.PropertyFinder)]
        PropertyFinder,
        [Description(EnumDescription.LeadSource.Bayut)]
        Bayut,
        [Description(EnumDescription.LeadSource.Dubizzle)]
        Dubizzle,
        [Description(EnumDescription.LeadSource.Webhook)]
        Webhook,
        [Description(EnumDescription.LeadSource.TikTok)]
        TikTok,
        [Description(EnumDescription.LeadSource.Snapchat)]
        Snapchat,
        [Description(EnumDescription.LeadSource.GoogleAdsCampaign)]
        GoogleAdsCampaign
    }
    #endregion

    #region ContactType
    public enum ContactType
    {
        [Description("WhatsApp")]
        WhatsApp = 0,
        [Description("Call")]
        Call,
        [Description("Email")]
        Email,
        [Description("SMS")]
        SMS,
        [Description("Push Notification")]
        PushNotification
    }
    #endregion

    #region Profession
    public enum Profession
    {
        None = 0,
        Salaried,
        Business,
        SelfEmployed,
        Doctor,
        Retired,
        Housewife,
        Student,
        Unemployed,
        Others
    }
    #endregion

    #region Upload Type
    public enum UploadType
    {
        None = 0,
        [Description("Excel Upload")]
        Excel,
        [Description("Json")]
        Json,
        [Description("CSV")]
        CSV,
        [Description("XML")]
        XML,
        [Description("Migration")]
        Migration
    }
    #endregion

    #region Furnish Status
    public enum FurnishStatus
    {
        [Description("None")]
        None = 0,
        [Description("Unfurnished")]
        Unfurnished,
        [Description("Semifurnished")]
        Semifurnished,
        [Description("Furnished")]
        Furnished
    }
    #endregion

    #region Offer Type
    public enum OfferType
    {
        None = 0,
        [Description("Ready")]
        Ready,
        [Description("OffPlan")]
        OffPlan,
        [Description("Secondary")]
        Secondary
    }
    #endregion

    #region Bulk Type
    public enum BulkType
    {
        None = 0,
        [Description("bulk assignment")]
        BulkAssignment,
        [Description("bulk update status")]
        BulkUpdateStatus,
        [Description("bulk delete")]
        BulkDelete,
        [Description("bulk email")]
        BulkEmail,
        [Description("bulk source")]
        BulkSource,
        [Description("bulk project")]
        BulkProject,
        [Description("bulk whatsapp")]
        BulkWhatsApp,
        [Description("bulk upload")]
        BulkUpload,
        [Description("bulk migrate")]
        BulkMigrate,
        [Description("bulk secondary assignment")]
        BulkSecondaryAssignment
    }
    #endregion

    #region Lead Assignment Type
    public enum LeadAssignmentType
    {
        WithHistory = 0,
        WithoutHistory,
        WithoutHistoryWithNewStatus
    }
    #endregion

    #region Marital Status Type
    public enum MaritalStatusType
    {
        [Description("NotMentioned")]
        NotMentioned = 0,
        [Description("Single")]
        Single,
        [Description("Married")]
        Married
    }
    #endregion

    #region Gender
    public enum Gender
    {
        [Description("NotMentioned")]
        NotMentioned = 0,
        [Description("Male")]
        Male,
        [Description("Female")]
        Female,
        [Description("Other")]
        Other
    }
    #endregion

    #region Possesion Type
    public enum PossesionType
    {
        None,
        UnderConstruction,
        SixMonth,
        Year,
        TwoYears,
        CustomDate,
        Immediate
    }
    #endregion

    #region Purpose
    public enum Purpose
    {
        None = 0,
        [Description("Investment")]
        Investment,
        [Description("SelfUse")]
        SelfUse
    }
    #endregion
}
