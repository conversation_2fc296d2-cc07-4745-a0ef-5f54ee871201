{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A6FEA220-9A45-4F59-A2D8-DFAE9990C977}|..\\LeadHistoryMigration.Infra\\LeadHistoryMigration.Infra.csproj|c:\\users\\<USER>\\desktop\\my project\\lead history\\leadhistorymigration.infra\\repos\\leadhistoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B26A6DDC-86E3-4580-B033-56B7EE44ECBC}|LeadHistoryMigration.csproj|c:\\users\\<USER>\\desktop\\my project\\lead history\\leadhistorymigration\\app.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B26A6DDC-86E3-4580-B033-56B7EE44ECBC}|LeadHistoryMigration.csproj|solutionrelative:app.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B26A6DDC-86E3-4580-B033-56B7EE44ECBC}|LeadHistoryMigration.csproj|c:\\users\\<USER>\\desktop\\my project\\lead history\\leadhistorymigration\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B26A6DDC-86E3-4580-B033-56B7EE44ECBC}|LeadHistoryMigration.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{980AB9CF-225D-4838-8892-3D06AD4A8565}|..\\LeadHistoryMigration.Application\\LeadHistoryMigration.Application.csproj|c:\\users\\<USER>\\desktop\\my project\\lead history\\leadhistorymigration.application\\existinghistory\\leadhistory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6FEA220-9A45-4F59-A2D8-DFAE9990C977}|..\\LeadHistoryMigration.Infra\\LeadHistoryMigration.Infra.csproj|c:\\users\\<USER>\\desktop\\my project\\lead history\\leadhistorymigration.infra\\database\\dapperconnectionfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A6FEA220-9A45-4F59-A2D8-DFAE9990C977}|..\\LeadHistoryMigration.Infra\\LeadHistoryMigration.Infra.csproj|c:\\users\\<USER>\\desktop\\my project\\lead history\\leadhistorymigration.infra\\startup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "LeadHistory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Application\\ExistingHistory\\LeadHistory.cs", "RelativeDocumentMoniker": "..\\LeadHistoryMigration.Application\\ExistingHistory\\LeadHistory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Application\\ExistingHistory\\LeadHistory.cs", "RelativeToolTip": "..\\LeadHistoryMigration.Application\\ExistingHistory\\LeadHistory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-29T06:46:58.937Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DapperConnectionFactory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Infra\\Database\\DapperConnectionFactory.cs", "RelativeDocumentMoniker": "..\\LeadHistoryMigration.Infra\\Database\\DapperConnectionFactory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Infra\\Database\\DapperConnectionFactory.cs", "RelativeToolTip": "..\\LeadHistoryMigration.Infra\\Database\\DapperConnectionFactory.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAnwFcAAABOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-29T06:11:57.154Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "LeadHistoryRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Infra\\Repos\\LeadHistoryRepository.cs", "RelativeDocumentMoniker": "..\\LeadHistoryMigration.Infra\\Repos\\LeadHistoryRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Infra\\Repos\\LeadHistoryRepository.cs", "RelativeToolTip": "..\\LeadHistoryMigration.Infra\\Repos\\LeadHistoryRepository.cs", "ViewState": "AgIAALYAAAAAAAAAAAAkwL8AAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-29T06:10:31.198Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "App.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration\\App.cs", "RelativeDocumentMoniker": "App.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration\\App.cs", "RelativeToolTip": "App.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAawCsAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-26T10:27:08.399Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "StartUp.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Infra\\StartUp.cs", "RelativeDocumentMoniker": "..\\LeadHistoryMigration.Infra\\StartUp.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration.Infra\\StartUp.cs", "RelativeToolTip": "..\\LeadHistoryMigration.Infra\\StartUp.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAnwBIAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-26T09:38:30.481Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\My Project\\Lead History\\LeadHistoryMigration\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-26T06:25:06.366Z", "EditorCaption": ""}]}]}]}