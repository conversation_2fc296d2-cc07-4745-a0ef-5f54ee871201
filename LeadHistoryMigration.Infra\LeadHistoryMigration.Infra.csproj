﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\LeadHistoryMigration</DockerfileContext>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
		<PackageReference Include="Dapper" Version="2.1.35" />
		<PackageReference Include="Npgsql" Version="8.0.5" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.0" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LeadHistoryMigration.Application\LeadHistoryMigration.Application.csproj" />
  </ItemGroup>

</Project>
