﻿using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Infra.Database;
using LeadHistoryMigration.Infra.NewLeadHistory;
using LeadHistoryMigration.Infra.Repos;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace LeadHistoryMigration.Infra
{
    /// <summary>
    /// Infrastructure layer dependency injection configuration.
    /// Configures both Entity Framework (for legacy support) and Dapper repositories.
    /// </summary>
    public static class StartUp
    {
        public static IServiceCollection AddInfra(this IServiceCollection services, IConfiguration config)
        {
            // Configure database settings
            var databaseSettings = new DatabaseSettings();
            config.GetSection("DatabaseSettings").Bind(databaseSettings);
            services.AddSingleton(databaseSettings);
            services.AddSingleton<Microsoft.Extensions.Options.IOptions<DatabaseSettings>>(
                provider => Microsoft.Extensions.Options.Options.Create(databaseSettings));

            // Register logging services
            services.AddLogging();

            // Register Dapper JSON type handlers for dictionary properties
            JsonTypeHandlers.RegisterTypeHandlers();

            // Register Dapper connection factory
            services.AddSingleton<IDapperConnectionFactory, DapperConnectionFactory>();

            // Keep Entity Framework for backward compatibility (can be removed later)
            services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(
                config.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName)
                )
            );

            // Register Dapper-based repositories
            services.AddTransient<ILeadHistoryRepository, LeadHistoryRepository>();
            services.AddTransient<ILeadHistoryHotRepository, LeadHistoryHotRepository>();
            services.AddTransient<ILeadHistoryWarmRepository, LeadHistoryWarmRepository>();
            services.AddTransient<ILeadHistoryColdRepository, LeadHistoryColdRepository>();

            // Register transformer with repository dependencies
            services.AddTransient<ILeadHistoryTransformer>(provider =>
            {
                var hotRepository = provider.GetRequiredService<ILeadHistoryHotRepository>();
                var warmRepository = provider.GetService<ILeadHistoryWarmRepository>();
                var coldRepository = provider.GetService<ILeadHistoryColdRepository>();

                return new LeadHistoryTransformer(
                    TransformationOptions.Default,
                    hotRepository,
                    warmRepository,
                    coldRepository);
            });

            return services;
        }
    }
}
