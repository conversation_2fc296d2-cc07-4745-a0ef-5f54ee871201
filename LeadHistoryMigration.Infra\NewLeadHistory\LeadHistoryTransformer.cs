﻿using LeadHistoryMigration.Application.ExistingHistory;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Application.Interfaces;
using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;

namespace LeadHistoryMigration.Infra.NewLeadHistory
{
    /// <summary>
    /// Transforms lead history data from versioned dictionary format to field-wise change records.
    /// Optimized for performance and memory efficiency with comprehensive error handling.
    /// </summary>
    public class LeadHistoryTransformer : ILeadHistoryTransformer
    {
        // Constants for default configuration values
        private const string DefaultTenantId = "ratify";
        private const string DateTimeFormat = "MM/dd/yyyy HH:mm:ss";
        private const int ParallelProcessingThreshold = 10;

        // Thread-safe cache for field metadata to avoid reflection overhead
        private static readonly ConcurrentDictionary<string, FieldMetadata> _fieldMetadataCache = new();

        // Thread-safe cache for enum string conversions to improve performance
        private static readonly ConcurrentDictionary<Enum, string> _enumStringCache = new();

        // Configuration for transformation behavior
        private readonly TransformationOptions _options;

        // Repository dependencies for data persistence
        private readonly ILeadHistoryHotRepository? _hotRepository;
        private readonly ILeadHistoryWarmRepository? _warmRepository;
        private readonly ILeadHistoryColdRepository? _coldRepository;

        /// <summary>
        /// Initializes a new instance of the LeadHistoryTransformer with default options.
        /// Uses optimized settings for typical lead history transformation scenarios.
        /// </summary>
        public LeadHistoryTransformer() : this(TransformationOptions.Default)
        {
        }

        /// <summary>
        /// Initializes a new instance of the LeadHistoryTransformer with custom options.
        /// Validates configuration and prepares the transformer for optimal performance.
        /// </summary>
        /// <param name="options">Configuration options for transformation behavior</param>
        /// <exception cref="ArgumentNullException">Thrown when options is null</exception>
        /// <exception cref="InvalidOperationException">Thrown when options validation fails</exception>
        public LeadHistoryTransformer(TransformationOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));

            // Validate options on construction to fail fast
            _options.Validate();

            LogInformation($"LeadHistoryTransformer initialized with custom options. " +
                          $"Parallel threshold: {_options.ParallelProcessingThreshold}, " +
                          $"Max parallelism: {_options.MaxDegreeOfParallelism}, " +
                          $"String caching: {_options.EnableStringCaching}");
        }

        /// <summary>
        /// Initializes a new instance of the LeadHistoryTransformer with repositories for data persistence.
        /// Enables both transformation and persistence capabilities.
        /// </summary>
        /// <param name="options">Configuration options for transformation behavior</param>
        /// <param name="hotRepository">Repository for hot storage (frequently accessed data)</param>
        /// <param name="warmRepository">Repository for warm storage (moderately accessed data)</param>
        /// <param name="coldRepository">Repository for cold storage (rarely accessed data)</param>
        public LeadHistoryTransformer(
            TransformationOptions options,
            ILeadHistoryHotRepository hotRepository,
            ILeadHistoryWarmRepository? warmRepository = null,
            ILeadHistoryColdRepository? coldRepository = null)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _hotRepository = hotRepository ?? throw new ArgumentNullException(nameof(hotRepository));
            _warmRepository = warmRepository;
            _coldRepository = coldRepository;

            // Validate options on construction to fail fast
            _options.Validate();

            LogInformation($"LeadHistoryTransformer initialized with repositories. " +
                          $"Hot: {hotRepository != null}, Warm: {warmRepository != null}, Cold: {coldRepository != null}");
        }

        /// <summary>
        /// Transforms a single lead history record to field-wise change records.
        /// </summary>
        /// <param name="leadHistory">The lead history record to transform</param>
        /// <returns>List of field-wise change records</returns>
        /// <exception cref="ArgumentNullException">Thrown when leadHistory is null</exception>
        public List<LeadHistoryHot> TransformToFieldWise(LeadHistory leadHistory)
        {
            if (leadHistory == null)
                throw new ArgumentNullException(nameof(leadHistory));

            // Validate lead history if strict validation is enabled
            ValidateLeadHistory(leadHistory);

            LogInformation($"Starting transformation for lead history {leadHistory.Id}");

            var fieldWiseRecords = new List<LeadHistoryHot>();

            // Pre-cache common dictionaries to avoid repeated null checks
            var modifiedDates = leadHistory.ModifiedDate ?? new Dictionary<int, DateTime>();
            var lastModifiedByUsers = leadHistory.LastModifiedBy?.ToDictionary(
                kvp => kvp.Key,
                kvp => Guid.TryParse(kvp.Value, out var guid) ? guid : leadHistory.CreatedBy
            ) ?? new Dictionary<int, Guid>();
            var assignedToUsers = leadHistory.AssignedToUser ?? new Dictionary<int, string>();

            // Generate a group key for this transformation batch
            var groupKey = Guid.NewGuid();

            // Process all JSONB fields using optimized method with field mapping
            ProcessAllFields(fieldWiseRecords, leadHistory, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            LogInformation($"Completed transformation for lead history {leadHistory.Id}. Generated {fieldWiseRecords.Count} field-wise records");

            return fieldWiseRecords;
        }

        /// <summary>
        /// Processes all fields using a structured approach to reduce code duplication.
        /// </summary>
        private void ProcessAllFields(
            List<LeadHistoryHot> fieldWiseRecords,
            LeadHistory leadHistory,
            IDictionary<int, DateTime> modifiedDates,
            IDictionary<int, Guid> lastModifiedByUsers,
            IDictionary<int, string> assignedToUsers,
            Guid groupKey)
        {
            // Define field mappings for better maintainability
            var fieldMappings = GetFieldMappings(leadHistory);

            // Process each field mapping
            foreach (var mapping in fieldMappings)
            {
                ProcessFieldMapping(fieldWiseRecords, leadHistory, mapping, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
            }
        }

        /// <summary>
        /// Gets the field mappings for lead history transformation with correct dictionary types.
        /// </summary>
        private static IEnumerable<FieldMapping> GetFieldMappings(LeadHistory leadHistory)
        {
            return new[]
            {
                new FieldMapping("Name", typeof(IDictionary<int, string>), leadHistory.Name),
                new FieldMapping("Contact No", typeof(IDictionary<int, string>), leadHistory.ContactNo),
                new FieldMapping("Email", typeof(IDictionary<int, string>), leadHistory.Email),
                new FieldMapping("Alternate Contact No", typeof(IDictionary<int, string>), leadHistory.AlternateContactNo),
                new FieldMapping("Lead Number", typeof(IDictionary<int, string>), leadHistory.LeadNumber),
                new FieldMapping("Notes", typeof(IDictionary<int, string>), leadHistory.Notes),
                new FieldMapping("Confidential Notes", typeof(IDictionary<int, string>), leadHistory.ConfidentialNotes),
                new FieldMapping("Lower Budget", typeof(IDictionary<int, long>), leadHistory.LowerBudget),
                new FieldMapping("Upper Budget", typeof(IDictionary<int, long>), leadHistory.UpperBudget),
                new FieldMapping("Area", typeof(IDictionary<int, double>), leadHistory.Area),
                new FieldMapping("Area Unit", typeof(IDictionary<int, string>), leadHistory.AreaUnit),
                new FieldMapping("Currency", typeof(IDictionary<int, string>), leadHistory.Currency),
                new FieldMapping("Base Lead Status", typeof(IDictionary<int, string>), leadHistory.BaseLeadStatus),
                new FieldMapping("Sub Lead Status", typeof(IDictionary<int, string>), leadHistory.SubLeadStatus),
                new FieldMapping("Rating", typeof(IDictionary<int, string>), leadHistory.Rating),
                new FieldMapping("Scheduled Date", typeof(IDictionary<int, DateTime?>), leadHistory.ScheduledDate),
                new FieldMapping("Picked Date", typeof(IDictionary<int, DateTime?>), leadHistory.PickedDate),
                new FieldMapping("Is Picked", typeof(IDictionary<int, bool>), leadHistory.IsPicked),
                new FieldMapping("Assign To", typeof(IDictionary<int, Guid>), leadHistory.AssignedTo),
                new FieldMapping("Assigned User", typeof(IDictionary<int, string>), leadHistory.AssignedToUser),
                new FieldMapping("Assigned From User", typeof(IDictionary<int, string>), leadHistory.AssignedFromUser),
                new FieldMapping("Customer City", typeof(IDictionary<int, string>), leadHistory.CustomerCity),
                new FieldMapping("Customer State", typeof(IDictionary<int, string>), leadHistory.CustomerState),
                new FieldMapping("Customer Location", typeof(IDictionary<int, string>), leadHistory.CustomerLocation),
                new FieldMapping("Customer Country", typeof(IDictionary<int, string>), leadHistory.CustomerCountry),
                new FieldMapping("Enquired City", typeof(IDictionary<int, string>), leadHistory.EnquiredCity),
                new FieldMapping("Enquired State", typeof(IDictionary<int, string>), leadHistory.EnquiredState),
                new FieldMapping("Enquired Location", typeof(IDictionary<int, string>), leadHistory.EnquiredLocation),
                new FieldMapping("Enquired Country", typeof(IDictionary<int, string>), leadHistory.EnquiredCountry),
                new FieldMapping("Lead Source", typeof(IDictionary<int, LeadSource>), leadHistory.LeadSource),
                new FieldMapping("Enquired For", typeof(IDictionary<int, EnquiryType>), leadHistory.EnquiredFor),
                new FieldMapping("Sale Type", typeof(IDictionary<int, SaleType>), leadHistory.SaleType),
                new FieldMapping("Is Hot Lead", typeof(IDictionary<int, bool>), leadHistory.IsHotLead),
                new FieldMapping("Is Cold Lead", typeof(IDictionary<int, bool>), leadHistory.IsColdLead),
                new FieldMapping("Is Warm Lead", typeof(IDictionary<int, bool>), leadHistory.IsWarmLead)
            };
        }

        /// <summary>
        /// Processes a single field mapping using optimized direct method calls instead of reflection.
        /// </summary>
        private void ProcessFieldMapping(
            List<LeadHistoryHot> fieldWiseRecords,
            LeadHistory leadHistory,
            FieldMapping mapping,
            IDictionary<int, DateTime> modifiedDates,
            IDictionary<int, Guid> lastModifiedByUsers,
            IDictionary<int, string> assignedToUsers,
            Guid groupKey)
        {
            // Use direct method calls based on value type to avoid reflection overhead
            switch (mapping.ValueType.Name)
            {
                case nameof(String):
                    ProcessDictionaryFieldOptimized<string>(fieldWiseRecords, leadHistory, mapping.FieldName,
                        mapping.FieldType, mapping.FieldData as IDictionary<int, string>,
                        modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
                    break;
                case nameof(Int64):
                    ProcessDictionaryFieldOptimized<long>(fieldWiseRecords, leadHistory, mapping.FieldName,
                        mapping.FieldType, mapping.FieldData as IDictionary<int, long>,
                        modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
                    break;
                case nameof(Double):
                    ProcessDictionaryFieldOptimized<double>(fieldWiseRecords, leadHistory, mapping.FieldName,
                        mapping.FieldType, mapping.FieldData as IDictionary<int, double>,
                        modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
                    break;
                case nameof(Boolean):
                    ProcessDictionaryFieldOptimized<bool>(fieldWiseRecords, leadHistory, mapping.FieldName,
                        mapping.FieldType, mapping.FieldData as IDictionary<int, bool>,
                        modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
                    break;
                case nameof(Guid):
                    ProcessDictionaryFieldOptimized<Guid>(fieldWiseRecords, leadHistory, mapping.FieldName,
                        mapping.FieldType, mapping.FieldData as IDictionary<int, Guid>,
                        modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
                    break;
                case "DateTime":
                    ProcessNullableDictionaryField<DateTime>(fieldWiseRecords, leadHistory, mapping.FieldName,
                        mapping.FieldType, mapping.FieldData as IDictionary<int, DateTime?>,
                        modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
                    break;
                default:
                    // Handle enums and other types
                    if (mapping.ValueType.IsEnum)
                    {
                        ProcessEnumField(fieldWiseRecords, leadHistory, mapping, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
                    }
                    break;
            }
        }

        /// <summary>
        /// Processes enum fields with optimized handling.
        /// </summary>
        private void ProcessEnumField(
            List<LeadHistoryHot> fieldWiseRecords,
            LeadHistory leadHistory,
            FieldMapping mapping,
            IDictionary<int, DateTime> modifiedDates,
            IDictionary<int, Guid> lastModifiedByUsers,
            IDictionary<int, string> assignedToUsers,
            Guid groupKey)
        {
            if (mapping.ValueType == typeof(LeadSource))
            {
                ProcessDictionaryFieldOptimized<LeadSource>(fieldWiseRecords, leadHistory, mapping.FieldName,
                    mapping.FieldType, mapping.FieldData as IDictionary<int, LeadSource>,
                    modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
            }
            else if (mapping.ValueType == typeof(EnquiryType))
            {
                ProcessDictionaryFieldOptimized<EnquiryType>(fieldWiseRecords, leadHistory, mapping.FieldName,
                    mapping.FieldType, mapping.FieldData as IDictionary<int, EnquiryType>,
                    modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
            }
            else if (mapping.ValueType == typeof(SaleType))
            {
                ProcessDictionaryFieldOptimized<SaleType>(fieldWiseRecords, leadHistory, mapping.FieldName,
                    mapping.FieldType, mapping.FieldData as IDictionary<int, SaleType>,
                    modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);
            }
        }

        /// <summary>
        /// Transforms multiple lead history records to field-wise change records asynchronously.
        /// Optimized for parallel processing and memory efficiency.
        /// </summary>
        /// <param name="leadHistories">The collection of lead history records to transform</param>
        /// <returns>Task containing the list of all field-wise change records</returns>
        /// <exception cref="ArgumentNullException">Thrown when leadHistories is null</exception>
        public async Task<List<LeadHistoryHot>> TransformMultipleToFieldWiseAsync(List<LeadHistory> leadHistories)
        {
            if (leadHistories == null)
                throw new ArgumentNullException(nameof(leadHistories));

            if (leadHistories.Count == 0)
                return new List<LeadHistoryHot>();

            // For small collections, process synchronously to avoid task overhead
            if (leadHistories.Count <= _options.ParallelProcessingThreshold)
            {
                return ProcessSequentially(leadHistories);
            }

            // For larger collections, use optimized parallel processing
            return await ProcessInParallelAsync(leadHistories);
        }

        /// <summary>
        /// Processes lead histories sequentially for small collections with enhanced error handling.
        /// </summary>
        private List<LeadHistoryHot> ProcessSequentially(List<LeadHistory> leadHistories)
        {
            if (leadHistories == null || leadHistories.Count == 0)
                return new List<LeadHistoryHot>();

            var result = new List<LeadHistoryHot>(leadHistories.Count * _options.EstimatedRecordsPerLead);
            var errorCount = 0;
            const int maxErrors = 10; // Prevent excessive error logging

            foreach (var leadHistory in leadHistories)
            {
                try
                {
                    if (leadHistory == null)
                    {
                        LogWarning("Encountered null lead history in collection, skipping");
                        continue;
                    }

                    var transformedRecords = TransformToFieldWise(leadHistory);
                    result.AddRange(transformedRecords);
                }
                catch (ArgumentException ex) when (_options.ContinueOnError)
                {
                    errorCount++;
                    LogError($"Validation error processing lead history {leadHistory?.Id}: {ex.Message}", ex);

                    if (errorCount >= maxErrors)
                    {
                        LogError($"Maximum error threshold ({maxErrors}) reached, stopping sequential processing", ex);
                        break;
                    }
                }
                catch (Exception ex) when (_options.ContinueOnError)
                {
                    errorCount++;
                    LogError($"Unexpected error processing lead history {leadHistory?.Id}: {ex.Message}", ex);

                    if (errorCount >= maxErrors)
                    {
                        LogError($"Maximum error threshold ({maxErrors}) reached, stopping sequential processing", ex);
                        break;
                    }
                }
            }

            LogInformation($"Sequential processing completed. Processed {result.Count} records with {errorCount} errors");
            return result;
        }

        /// <summary>
        /// Processes lead histories in parallel for large collections with enhanced error handling and memory optimization.
        /// </summary>
        private async Task<List<LeadHistoryHot>> ProcessInParallelAsync(List<LeadHistory> leadHistories)
        {
            if (leadHistories == null || leadHistories.Count == 0)
                return new List<LeadHistoryHot>();

            var parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = _options.MaxDegreeOfParallelism > 0
                    ? _options.MaxDegreeOfParallelism
                    : Environment.ProcessorCount,
                CancellationToken = CancellationToken.None
            };

            var allFieldWiseRecords = new ConcurrentBag<LeadHistoryHot>();
            var errorCount = 0;
            const int maxErrors = 50; // Higher threshold for parallel processing

            await Task.Run(() =>
            {
                try
                {
                    Parallel.ForEach(leadHistories, parallelOptions, (leadHistory, loopState) =>
                    {
                        try
                        {
                            if (leadHistory == null)
                            {
                                LogWarning("Encountered null lead history in parallel processing, skipping");
                                return;
                            }

                            var records = TransformToFieldWise(leadHistory);

                            // Batch add records to reduce contention
                            foreach (var record in records)
                            {
                                allFieldWiseRecords.Add(record);
                            }
                        }
                        catch (ArgumentException ex) when (_options.ContinueOnError)
                        {
                            Interlocked.Increment(ref errorCount);
                            LogError($"Validation error in parallel processing for lead history {leadHistory?.Id}: {ex.Message}", ex);

                            if (errorCount >= maxErrors)
                            {
                                LogError($"Maximum error threshold ({maxErrors}) reached, stopping parallel processing", ex);
                                loopState.Stop();
                            }
                        }
                        catch (Exception ex) when (_options.ContinueOnError)
                        {
                            Interlocked.Increment(ref errorCount);
                            LogError($"Unexpected error in parallel processing for lead history {leadHistory?.Id}: {ex.Message}", ex);

                            if (errorCount >= maxErrors)
                            {
                                LogError($"Maximum error threshold ({maxErrors}) reached, stopping parallel processing", ex);
                                loopState.Stop();
                            }
                        }
                    });
                }
                catch (Exception ex)
                {
                    LogError($"Critical error in parallel processing: {ex.Message}", ex);
                    throw; // Re-throw critical errors
                }
            });

            LogInformation($"Parallel processing completed. Processed {allFieldWiseRecords.Count} records with {errorCount} errors");
            return allFieldWiseRecords.ToList();
        }

        /// <summary>
        /// Optimized method to process dictionary fields and create field-wise change records.
        /// Uses efficient dictionary lookups, reduces object allocations, and optimizes sorting.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void ProcessDictionaryFieldOptimized<T>(
            List<LeadHistoryHot> records,
            LeadHistory leadHistory,
            string fieldName,
            Type fieldType,
            IDictionary<int, T>? fieldData,
            IDictionary<int, DateTime> modifiedDates,
            IDictionary<int, Guid> lastModifiedByUsers,
            IDictionary<int, string> assignedToUsers,
            Guid groupKey) where T : notnull
        {
            if (fieldData == null || fieldData.Count == 0) return;

            // Get or create field metadata for type information
            var fieldMetadata = GetFieldMetadata(fieldName, fieldType);

            // Optimize sorting: use array sorting for better performance
            var sortedVersions = GetSortedVersions(fieldData);

            T? previousValue = default;
            bool hasPreviousValue = false;

            // Use object pooling for string conversions to reduce GC pressure
            var stringConversions = _options.EnableStringCaching
                ? new Dictionary<T, string?>(fieldData.Count)
                : null;

            // Pre-allocate StringBuilder for string operations
            var stringBuilder = new StringBuilder(256);

            for (int i = 0; i < sortedVersions.Length; i++)
            {
                var (currentVersion, currentValue) = sortedVersions[i];

                // Use optimized string conversions
                string? oldValue = hasPreviousValue
                    ? GetOrConvertToStringOptimized(previousValue, fieldMetadata, stringConversions, stringBuilder)
                    : null;
                string? newValue = GetOrConvertToStringOptimized(currentValue, fieldMetadata, stringConversions, stringBuilder);

                // Only create record if there's a meaningful change
                if (!string.Equals(oldValue, newValue, StringComparison.Ordinal))
                {
                    // Use efficient dictionary lookups with fallbacks
                    var modifiedDate = modifiedDates.TryGetValue(currentVersion, out var modDate)
                        ? modDate
                        : leadHistory.CreatedDate;

                    var lastModifiedBy = lastModifiedByUsers.TryGetValue(currentVersion, out var modById)
                        ? modById
                        : leadHistory.CreatedBy;

                    var modifiedByUserName = assignedToUsers.TryGetValue(currentVersion, out var userName)
                        ? userName
                        : string.Empty;

                    records.Add(CreateLeadHistoryHotRecord(
                        leadHistory, fieldMetadata, oldValue, newValue, modifiedByUserName,
                        modifiedDate, lastModifiedBy, currentVersion, groupKey));
                }

                previousValue = currentValue;
                hasPreviousValue = true;
            }
        }

        /// <summary>
        /// Processes nullable dictionary fields (like DateTime?) that don't satisfy notnull constraint.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void ProcessNullableDictionaryField<T>(
            List<LeadHistoryHot> records,
            LeadHistory leadHistory,
            string fieldName,
            Type fieldType,
            IDictionary<int, T?>? fieldData,
            IDictionary<int, DateTime> modifiedDates,
            IDictionary<int, Guid> lastModifiedByUsers,
            IDictionary<int, string> assignedToUsers,
            Guid groupKey) where T : struct
        {
            if (fieldData == null || fieldData.Count == 0)
                return;

            var fieldMetadata = GetFieldMetadata(fieldName, fieldType);

            // Sort by version for consistent processing
            var sortedData = fieldData.OrderBy(x => x.Key).ToArray();

            T? previousValue = default;
            bool hasPreviousValue = false;

            // Pre-allocate StringBuilder for string operations
            var stringBuilder = new StringBuilder(256);

            foreach (var (currentVersion, currentValue) in sortedData)
            {
                // Clear StringBuilder for reuse
                stringBuilder.Clear();

                // Only create record if there's a meaningful change
                var oldValue = hasPreviousValue ? ConvertToStringOptimized(previousValue, fieldMetadata, stringBuilder) : null;
                var newValue = ConvertToStringOptimized(currentValue, fieldMetadata, stringBuilder);

                // Only create record if there's a meaningful change
                if (!string.Equals(oldValue, newValue, StringComparison.Ordinal))
                {
                    // Use efficient dictionary lookups with fallbacks
                    var modifiedDate = modifiedDates.TryGetValue(currentVersion, out var modDate)
                        ? modDate
                        : leadHistory.CreatedDate;

                    var lastModifiedBy = lastModifiedByUsers.TryGetValue(currentVersion, out var modById)
                        ? modById
                        : leadHistory.CreatedBy;

                    var modifiedByUserName = assignedToUsers.TryGetValue(currentVersion, out var userName)
                        ? userName
                        : string.Empty;

                    records.Add(CreateLeadHistoryHotRecord(
                        leadHistory, fieldMetadata, oldValue, newValue, modifiedByUserName,
                        modifiedDate, lastModifiedBy, currentVersion, groupKey));
                }

                previousValue = currentValue;
                hasPreviousValue = true;
            }
        }

        /// <summary>
        /// Optimized sorting method that chooses the best algorithm based on collection size.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static KeyValuePair<int, T>[] GetSortedVersions<T>(IDictionary<int, T> fieldData) where T : notnull
        {
            // For small collections, use LINQ for simplicity
            if (fieldData.Count <= 5)
            {
                return fieldData.OrderBy(x => x.Key).ToArray();
            }

            // For larger collections, use array sorting for better performance
            var array = new KeyValuePair<int, T>[fieldData.Count];
            fieldData.CopyTo(array, 0);
            Array.Sort(array, (x, y) => x.Key.CompareTo(y.Key));
            return array;
        }

        /// <summary>
        /// Creates a LeadHistoryHot record with optimized object initialization.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private LeadHistoryHot CreateLeadHistoryHotRecord(
            LeadHistory leadHistory,
            FieldMetadata fieldMetadata,
            string? oldValue,
            string? newValue,
            string modifiedByUserName,
            DateTime modifiedDate,
            Guid lastModifiedBy,
            int version,
            Guid groupKey)
        {
            return new LeadHistoryHot
            {
                Id = Guid.NewGuid(),
                LeadId = leadHistory.LeadId,
                FieldName = fieldMetadata.FieldName,
                FieldType = fieldMetadata.TypeName,
                OldValue = oldValue,
                NewValue = newValue,
                ModifiedBy = modifiedByUserName,
                ModifiedOn = modifiedDate,
                LastModifiedById = lastModifiedBy,
                Version = version,
                GroupKey = groupKey,
                UserId = leadHistory.UserId,
                TenantId = _options.DefaultTenantId,
                IsDeleted = leadHistory.IsDeleted
            };
        }

        /// <summary>
        /// Gets or converts value to string with optional caching and StringBuilder reuse for performance.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private string? GetOrConvertToStringOptimized<T>(T? value, FieldMetadata metadata, Dictionary<T, string?>? cache, StringBuilder stringBuilder) where T : notnull
        {
            if (value == null) return null;

            // Use cache if enabled and available
            if (cache != null && cache.TryGetValue(value, out var cachedResult))
                return cachedResult;

            var result = ConvertToStringOptimized(value, metadata, stringBuilder);

            // Cache the result if caching is enabled
            if (cache != null)
                cache[value] = result;

            return result;
        }

        /// <summary>
        /// Optimized string conversion method with StringBuilder reuse and efficient type handling.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private string? ConvertToStringOptimized<T>(T? value, FieldMetadata metadata, StringBuilder stringBuilder)
        {
            if (value == null) return null;

            // Clear and reuse StringBuilder to reduce allocations
            stringBuilder.Clear();

            // Use cached type information for faster conversions with optimized paths
            return metadata.TypeCode switch
            {
                TypeCode.String => (string)(object)value,
                TypeCode.Boolean => (bool)(object)value ? "True" : "False",
                TypeCode.Int64 => FormatInt64((long)(object)value, stringBuilder),
                TypeCode.Double => FormatDouble((double)(object)value, stringBuilder),
                TypeCode.DateTime => FormatDateTime((DateTime)(object)value, stringBuilder),
                TypeCode.Object when value is Guid guid => guid.ToString("D"),
                TypeCode.Object when value is DateTime nullableDateTime =>
                    nullableDateTime.ToString(_options.DateTimeFormat, CultureInfo.InvariantCulture),
                TypeCode.Object when value is Enum enumValue => GetEnumStringCached(enumValue),
                _ => value.ToString()
            };
        }

        /// <summary>
        /// Optimized Int64 formatting using StringBuilder.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static string FormatInt64(long value, StringBuilder stringBuilder)
        {
            stringBuilder.Append(value);
            return stringBuilder.ToString();
        }

        /// <summary>
        /// Optimized Double formatting using StringBuilder.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static string FormatDouble(double value, StringBuilder stringBuilder)
        {
            stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "{0:G}", value);
            return stringBuilder.ToString();
        }

        /// <summary>
        /// Optimized DateTime formatting using StringBuilder.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private string FormatDateTime(DateTime value, StringBuilder stringBuilder)
        {
            stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "{0:" + _options.DateTimeFormat + "}", value);
            return stringBuilder.ToString();
        }

        /// <summary>
        /// Optimized enum to string conversion with static caching for better performance.
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static string GetEnumStringCached(Enum enumValue)
        {
            // Use static cache for enum string conversions to avoid repeated ToString() calls
            return _enumStringCache.GetOrAdd(enumValue, e => e.ToString());
        }



        /// <summary>
        /// Gets or creates field metadata for efficient type operations.
        /// </summary>
        private static FieldMetadata GetFieldMetadata(string fieldName, Type fieldType)
        {
            return _fieldMetadataCache.GetOrAdd(fieldName, _ => new FieldMetadata(fieldName, fieldType));
        }



        /// <summary>
        /// Logs errors during transformation with structured logging support.
        /// Override this method to integrate with your logging framework.
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="exception">Exception details</param>
        protected virtual void LogError(string message, Exception exception)
        {
            // Default implementation uses Debug output with structured information
            // In production, replace with your logging framework (e.g., ILogger, NLog, or other frameworks)
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
            System.Diagnostics.Debug.WriteLine($"[{timestamp}] [ERROR] [LeadHistoryTransformer] {message}");
            System.Diagnostics.Debug.WriteLine($"[{timestamp}] [ERROR] [LeadHistoryTransformer] Exception Type: {exception.GetType().Name}");
            System.Diagnostics.Debug.WriteLine($"[{timestamp}] [ERROR] [LeadHistoryTransformer] Exception Message: {exception.Message}");

            if (exception.InnerException != null)
            {
                System.Diagnostics.Debug.WriteLine($"[{timestamp}] [ERROR] [LeadHistoryTransformer] Inner Exception: {exception.InnerException.Message}");
            }

            System.Diagnostics.Debug.WriteLine($"[{timestamp}] [ERROR] [LeadHistoryTransformer] Stack Trace: {exception.StackTrace}");
        }

        /// <summary>
        /// Logs informational messages during transformation.
        /// </summary>
        /// <param name="message">Information message</param>
        protected virtual void LogInformation(string message)
        {
            if (_options.EnableVerboseLogging)
            {
                var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
                System.Diagnostics.Debug.WriteLine($"[{timestamp}] [INFO] [LeadHistoryTransformer] {message}");
            }
        }

        /// <summary>
        /// Logs warning messages during transformation.
        /// </summary>
        /// <param name="message">Warning message</param>
        protected virtual void LogWarning(string message)
        {
            if (_options.EnableVerboseLogging)
            {
                var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
                System.Diagnostics.Debug.WriteLine($"[{timestamp}] [WARN] [LeadHistoryTransformer] {message}");
            }
        }

        /// <summary>
        /// Validates the lead history object for required fields and data integrity with enhanced checks.
        /// </summary>
        /// <param name="leadHistory">The lead history to validate</param>
        /// <exception cref="ArgumentException">Thrown when validation fails</exception>
        private void ValidateLeadHistory(LeadHistory leadHistory)
        {
            if (!_options.StrictValidation) return;

            var validationErrors = new List<string>();

            // Required field validations
            if (leadHistory.LeadId == Guid.Empty)
                validationErrors.Add("LeadId cannot be empty");

            if (leadHistory.CreatedBy == Guid.Empty)
                validationErrors.Add("CreatedBy cannot be empty");

            if (leadHistory.CreatedDate == default)
                validationErrors.Add("CreatedDate cannot be default value");

            // Date range validations
            if (leadHistory.CreatedDate > DateTime.UtcNow.AddDays(1))
                validationErrors.Add("CreatedDate cannot be in the future");

            if (leadHistory.CreatedDate < new DateTime(2000, 1, 1))
                validationErrors.Add("CreatedDate cannot be before year 2000");

            // Dictionary consistency validations
            ValidateDictionaryConsistency(leadHistory, validationErrors);

            if (validationErrors.Count > 0)
            {
                var errorMessage = $"Lead history validation failed for ID {leadHistory.Id}: {string.Join("; ", validationErrors)}";
                throw new ArgumentException(errorMessage, nameof(leadHistory));
            }
        }

        /// <summary>
        /// Validates dictionary field consistency and data integrity.
        /// </summary>
        private static void ValidateDictionaryConsistency(LeadHistory leadHistory, List<string> validationErrors)
        {
            // Check for negative version numbers in dictionaries
            var dictionariesToCheck = new[]
            {
                ("ModifiedDate", leadHistory.ModifiedDate?.Keys),
                ("LastModifiedBy", leadHistory.LastModifiedBy?.Keys),
                ("AssignedToUser", leadHistory.AssignedToUser?.Keys)
            };

            foreach (var (name, keys) in dictionariesToCheck)
            {
                if (keys != null && keys.Any(k => k < 0))
                {
                    validationErrors.Add($"{name} dictionary contains negative version numbers");
                }
            }

            // Check for excessively large version numbers (potential data corruption)
            const int maxReasonableVersion = 10000;
            foreach (var (name, keys) in dictionariesToCheck)
            {
                if (keys != null && keys.Any(k => k > maxReasonableVersion))
                {
                    validationErrors.Add($"{name} dictionary contains unreasonably large version numbers (>{maxReasonableVersion})");
                }
            }
        }

        #region Persistence Methods

        /// <summary>
        /// Transforms and persists a single lead history record to the database.
        /// </summary>
        /// <param name="leadHistory">The lead history record to transform and persist</param>
        /// <returns>Number of records inserted</returns>
        /// <exception cref="ArgumentNullException">Thrown when leadHistory is null</exception>
        /// <exception cref="InvalidOperationException">Thrown when no hot repository is configured</exception>
        public async Task<int> TransformAndPersistAsync(LeadHistory leadHistory)
        {
            if (leadHistory == null)
                throw new ArgumentNullException(nameof(leadHistory));

            if (_hotRepository == null)
                throw new InvalidOperationException("Hot repository is required for persistence operations. Use constructor with repository parameters.");

            LogInformation($"Starting transform and persist for lead history {leadHistory.Id}");

            // Transform the data
            var transformedRecords = TransformToFieldWise(leadHistory);

            if (transformedRecords.Count == 0)
            {
                LogInformation($"No records to persist for lead history {leadHistory.Id}");
                return 0;
            }

            // Persist using bulk insert for better performance
            var insertedCount = await _hotRepository.BulkInsertAsync(transformedRecords);

            LogInformation($"Successfully persisted {insertedCount} records for lead history {leadHistory.Id}");
            return insertedCount;
        }

        /// <summary>
        /// Transforms and persists multiple lead history records to the database using bulk operations.
        /// </summary>
        /// <param name="leadHistories">The lead history records to transform and persist</param>
        /// <returns>Number of records inserted</returns>
        /// <exception cref="ArgumentNullException">Thrown when leadHistories is null</exception>
        /// <exception cref="InvalidOperationException">Thrown when no hot repository is configured</exception>
        public async Task<int> TransformAndPersistMultipleAsync(List<LeadHistory> leadHistories)
        {
            if (leadHistories == null)
                throw new ArgumentNullException(nameof(leadHistories));

            if (_hotRepository == null)
                throw new InvalidOperationException("Hot repository is required for persistence operations. Use constructor with repository parameters.");

            if (leadHistories.Count == 0)
            {
                LogInformation("No lead histories to process");
                return 0;
            }

            LogInformation($"Starting transform and persist for {leadHistories.Count} lead histories");

            // Transform all records
            var allTransformedRecords = await TransformMultipleToFieldWiseAsync(leadHistories);

            if (allTransformedRecords.Count == 0)
            {
                LogInformation("No records to persist after transformation");
                return 0;
            }

            // Persist using bulk insert for optimal performance
            var insertedCount = await _hotRepository.BulkInsertAsync(allTransformedRecords);

            LogInformation($"Successfully persisted {insertedCount} records from {leadHistories.Count} lead histories");
            return insertedCount;
        }

        /// <summary>
        /// Transforms and persists with transaction support for data consistency.
        /// Ensures all records are inserted successfully or none at all.
        /// </summary>
        /// <param name="leadHistories">The lead history records to transform and persist</param>
        /// <returns>Number of records inserted</returns>
        /// <exception cref="ArgumentNullException">Thrown when leadHistories is null</exception>
        /// <exception cref="InvalidOperationException">Thrown when no hot repository is configured</exception>
        public async Task<int> TransformAndPersistWithTransactionAsync(List<LeadHistory> leadHistories)
        {
            if (leadHistories == null)
                throw new ArgumentNullException(nameof(leadHistories));

            if (_hotRepository == null)
                throw new InvalidOperationException("Hot repository is required for persistence operations. Use constructor with repository parameters.");

            if (leadHistories.Count == 0)
            {
                LogInformation("No lead histories to process");
                return 0;
            }

            LogInformation($"Starting transactional transform and persist for {leadHistories.Count} lead histories");

            try
            {
                // Transform all records first
                var allTransformedRecords = await TransformMultipleToFieldWiseAsync(leadHistories);

                if (allTransformedRecords.Count == 0)
                {
                    LogInformation("No records to persist after transformation");
                    return 0;
                }

                // Persist with transaction support
                var insertedCount = await _hotRepository.BulkInsertAsync(allTransformedRecords);

                LogInformation($"Successfully persisted {insertedCount} records from {leadHistories.Count} lead histories with transaction");
                return insertedCount;
            }
            catch (Exception ex)
            {
                LogError($"Error during transactional transform and persist: {ex.Message}", ex);
                throw;
            }
        }

        #endregion
    }

    /// <summary>
    /// Represents a field mapping for lead history transformation.
    /// </summary>
    internal sealed class FieldMapping
    {
        public string FieldName { get; }
        public Type FieldType { get; }
        public Type ValueType { get; }
        public object? FieldData { get; }

        public FieldMapping(string fieldName, Type fieldType, object? fieldData)
        {
            FieldName = fieldName ?? throw new ArgumentNullException(nameof(fieldName));
            FieldType = fieldType ?? throw new ArgumentNullException(nameof(fieldType));
            FieldData = fieldData;

            // Extract the value type from the dictionary type
            if (fieldType.IsGenericType && fieldType.GetGenericTypeDefinition() == typeof(IDictionary<,>))
            {
                ValueType = fieldType.GetGenericArguments()[1];
            }
            else
            {
                ValueType = fieldType;
            }
        }
    }

    /// <summary>
    /// Metadata class for efficient field type operations and caching.
    /// </summary>
    internal sealed class FieldMetadata
    {
        public string FieldName { get; }
        public Type FieldType { get; }
        public string TypeName { get; }
        public TypeCode TypeCode { get; }

        public FieldMetadata(string fieldName, Type fieldType)
        {
            FieldName = fieldName ?? throw new ArgumentNullException(nameof(fieldName));
            FieldType = fieldType ?? throw new ArgumentNullException(nameof(fieldType));

            // Handle nullable types
            var underlyingType = Nullable.GetUnderlyingType(fieldType) ?? fieldType;
            TypeCode = Type.GetTypeCode(underlyingType);

            // Generate clean type name
            TypeName = GenerateTypeName(fieldType);
        }

        private static string GenerateTypeName(Type type)
        {
            if (type == typeof(string)) return "String";
            if (type == typeof(bool)) return "Boolean";
            if (type == typeof(long)) return "Int64";
            if (type == typeof(double)) return "Double";
            if (type == typeof(Guid)) return "Guid";
            if (type == typeof(DateTime?)) return "DateTime?";
            if (type == typeof(DateTime)) return "DateTime";
            if (type.IsEnum) return type.Name;

            return type.Name;
        }
    }

    /// <summary>
    /// Configuration options for the LeadHistoryTransformer with validation and enhanced settings.
    /// </summary>
    public sealed class TransformationOptions
    {
        private int _parallelProcessingThreshold = 10;
        private int _maxDegreeOfParallelism = -1;
        private string _defaultTenantId = "ratify";
        private string _dateTimeFormat = "MM/dd/yyyy HH:mm:ss";
        private int _estimatedRecordsPerLead = 30;

        /// <summary>
        /// Default configuration options.
        /// </summary>
        public static readonly TransformationOptions Default = new();

        /// <summary>
        /// Threshold for switching from sequential to parallel processing.
        /// Must be greater than 0. Default is 10 records.
        /// </summary>
        public int ParallelProcessingThreshold
        {
            get => _parallelProcessingThreshold;
            set
            {
                if (value <= 0)
                    throw new ArgumentOutOfRangeException(nameof(value), "ParallelProcessingThreshold must be greater than 0");
                _parallelProcessingThreshold = value;
            }
        }

        /// <summary>
        /// Whether to continue processing other records when an error occurs.
        /// Default is true.
        /// </summary>
        public bool ContinueOnError { get; set; } = true;

        /// <summary>
        /// Default tenant ID to use when not specified.
        /// Cannot be null or empty. Default is "ratify".
        /// </summary>
        public string DefaultTenantId
        {
            get => _defaultTenantId;
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("DefaultTenantId cannot be null or empty", nameof(value));
                _defaultTenantId = value;
            }
        }

        /// <summary>
        /// Date time format for string conversion.
        /// Cannot be null or empty. Default is "MM/dd/yyyy HH:mm:ss".
        /// </summary>
        public string DateTimeFormat
        {
            get => _dateTimeFormat;
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("DateTimeFormat cannot be null or empty", nameof(value));

                // Validate the format by trying to format a date
                try
                {
                    DateTime.Now.ToString(value, CultureInfo.InvariantCulture);
                }
                catch (FormatException ex)
                {
                    throw new ArgumentException($"Invalid DateTimeFormat: {value}", nameof(value), ex);
                }

                _dateTimeFormat = value;
            }
        }

        /// <summary>
        /// Maximum degree of parallelism for parallel processing.
        /// Must be -1 (unlimited) or greater than 0. Default is -1 (use all available processors).
        /// </summary>
        public int MaxDegreeOfParallelism
        {
            get => _maxDegreeOfParallelism;
            set
            {
                if (value != -1 && value <= 0)
                    throw new ArgumentOutOfRangeException(nameof(value), "MaxDegreeOfParallelism must be -1 or greater than 0");
                _maxDegreeOfParallelism = value;
            }
        }

        /// <summary>
        /// Whether to validate input parameters strictly.
        /// Default is true.
        /// </summary>
        public bool StrictValidation { get; set; } = true;

        /// <summary>
        /// Whether to enable verbose logging for debugging purposes.
        /// Default is false.
        /// </summary>
        public bool EnableVerboseLogging { get; set; } = false;

        /// <summary>
        /// Maximum number of records to process in a single batch.
        /// Must be greater than 0. Default is 1000.
        /// </summary>
        public int MaxBatchSize { get; set; } = 1000;

        /// <summary>
        /// Estimated number of field-wise records per lead history record.
        /// Used for pre-allocating collections. Default is 30.
        /// </summary>
        public int EstimatedRecordsPerLead
        {
            get => _estimatedRecordsPerLead;
            set
            {
                if (value <= 0)
                    throw new ArgumentOutOfRangeException(nameof(value), "EstimatedRecordsPerLead must be greater than 0");
                _estimatedRecordsPerLead = value;
            }
        }

        /// <summary>
        /// Whether to enable string caching for repeated value conversions.
        /// Default is true for better performance.
        /// </summary>
        public bool EnableStringCaching { get; set; } = true;

        /// <summary>
        /// Validates all configuration options with enhanced checks.
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown when configuration is invalid</exception>
        public void Validate()
        {
            var errors = new List<string>();

            if (ParallelProcessingThreshold <= 0)
                errors.Add("ParallelProcessingThreshold must be greater than 0");

            if (string.IsNullOrWhiteSpace(DefaultTenantId))
                errors.Add("DefaultTenantId cannot be null or empty");

            if (string.IsNullOrWhiteSpace(DateTimeFormat))
                errors.Add("DateTimeFormat cannot be null or empty");

            if (MaxDegreeOfParallelism != -1 && MaxDegreeOfParallelism <= 0)
                errors.Add("MaxDegreeOfParallelism must be -1 or greater than 0");

            if (MaxBatchSize <= 0)
                errors.Add("MaxBatchSize must be greater than 0");

            if (EstimatedRecordsPerLead <= 0)
                errors.Add("EstimatedRecordsPerLead must be greater than 0");

            // Validate reasonable limits
            if (MaxBatchSize > 100000)
                errors.Add("MaxBatchSize should not exceed 100,000 for memory efficiency");

            if (EstimatedRecordsPerLead > 1000)
                errors.Add("EstimatedRecordsPerLead should not exceed 1,000 for memory efficiency");

            if (errors.Count > 0)
            {
                throw new InvalidOperationException($"TransformationOptions validation failed: {string.Join("; ", errors)}");
            }
        }
    }
}
