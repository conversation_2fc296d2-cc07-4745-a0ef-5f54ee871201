﻿using LeadHistoryMigration.Application.ExistingHistory;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace LeadHistoryMigration.Infra.Database
{
    public class ApplicationDbContext : DbContext
    {
        private readonly DatabaseSettings _settings;

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IOptions<DatabaseSettings> settings)
            : base(options)
        {
            _settings = settings.Value;
        }

        public DbSet<LeadHistory> LeadHistories { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseNpgsql(_settings.ConnectionString, options =>
                {
                    options.CommandTimeout(_settings.CommandTimeout);
                    options.EnableRetryOnFailure(3);
                });
            }
        }
    }
}
