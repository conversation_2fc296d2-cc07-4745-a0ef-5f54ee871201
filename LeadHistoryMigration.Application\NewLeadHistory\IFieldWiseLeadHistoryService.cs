﻿namespace LeadHistoryMigration.Application.NewLeadHistory
{
    public interface IFieldWiseLeadHistoryService
    {
        Task<int> ConvertAndSaveLeadHistoryAsync(Guid leadId);
        Task<int> ConvertAndSaveBulkLeadHistoriesAsync(List<Guid> leadIds);
        Task<List<LeadHistoryHot>> GetFieldWiseHistoryAsync(Guid leadId);
        Task<List<LeadHistoryHot>> GetFieldWiseHistoryByVersionAsync(Guid leadId, int version);
    }
}
