using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Infra.Database;
using Dapper;
using System.Data;
using System.Text;
using Microsoft.Extensions.Logging;
using Npgsql;
using System.Diagnostics;

namespace LeadHistoryMigration.Infra.Repos
{
    /// <summary>
    /// High-performance Dapper-based repository for LeadHistoryHot entities.
    /// Optimized for bulk operations and frequent read/write scenarios with comprehensive error handling.
    /// </summary>
    public class LeadHistoryHotRepository : ILeadHistoryHotRepository
    {
        private readonly IDapperConnectionFactory _connectionFactory;
        private readonly ILogger<LeadHistoryHotRepository> _logger;

        public LeadHistoryHotRepository(IDapperConnectionFactory connectionFactory, ILogger<LeadHistoryHotRepository> logger)
        {
            _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a lead history hot record by its ID.
        /// </summary>
        public async Task<LeadHistoryHot?> GetByIdAsync(Guid id)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryHot""
                WHERE ""Id"" = @Id AND ""IsDeleted"" = false";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetByIdAsync for LeadHistoryHot with Id: {Id}", id);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryFirstOrDefaultAsync<LeadHistoryHot>(sql, new { Id = id });

                stopwatch.Stop();
                _logger.LogDebug("GetByIdAsync completed successfully in {ElapsedMs}ms for Id: {Id}",
                    stopwatch.ElapsedMilliseconds, id);

                return result;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByIdAsync for Id: {Id}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history hot record with ID {id}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByIdAsync for Id: {Id}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    id, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history hot record with ID {id}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByIdAsync for Id: {Id}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history hot record with ID {id}", ex);
            }
        }

        /// <summary>
        /// Gets all lead history hot records for a specific lead ID with pagination.
        /// </summary>
        public async Task<List<LeadHistoryHot>> GetByLeadIdAsync(Guid leadId, int pageNumber = 1, int pageSize = 100)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryHot""
                WHERE ""LeadId"" = @LeadId AND ""IsDeleted"" = false
                ORDER BY ""ModifiedOn"" DESC, ""Version"" DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                var offset = (pageNumber - 1) * pageSize;
                _logger.LogDebug("Executing GetByLeadIdAsync for LeadId: {LeadId}, PageNumber: {PageNumber}, PageSize: {PageSize}",
                    leadId, pageNumber, pageSize);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryAsync<LeadHistoryHot>(sql, new { LeadId = leadId, Offset = offset, PageSize = pageSize });
                var resultList = result.ToList();

                stopwatch.Stop();
                _logger.LogDebug("GetByLeadIdAsync completed successfully in {ElapsedMs}ms for LeadId: {LeadId}. Records returned: {RecordCount}",
                    stopwatch.ElapsedMilliseconds, leadId, resultList.Count);

                return resultList;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByLeadIdAsync for LeadId: {LeadId}, PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, pageNumber, pageSize, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history hot records for lead ID {leadId} (page {pageNumber}, size {pageSize})", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByLeadIdAsync for LeadId: {LeadId}, PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    leadId, pageNumber, pageSize, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history hot records for lead ID {leadId} (page {pageNumber}, size {pageSize})", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByLeadIdAsync for LeadId: {LeadId}, PageNumber: {PageNumber}, PageSize: {PageSize}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, pageNumber, pageSize, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history hot records for lead ID {leadId} (page {pageNumber}, size {pageSize})", ex);
            }
        }

        /// <summary>
        /// Gets lead history hot records by group key (transformation batch).
        /// </summary>
        public async Task<List<LeadHistoryHot>> GetByGroupKeyAsync(Guid groupKey)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryHot""
                WHERE ""GroupKey"" = @GroupKey AND ""IsDeleted"" = false
                ORDER BY ""LeadId"", ""FieldName"", ""Version""";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetByGroupKeyAsync for GroupKey: {GroupKey}", groupKey);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryAsync<LeadHistoryHot>(sql, new { GroupKey = groupKey });
                var resultList = result.ToList();

                stopwatch.Stop();
                _logger.LogDebug("GetByGroupKeyAsync completed successfully in {ElapsedMs}ms for GroupKey: {GroupKey}. Records returned: {RecordCount}",
                    stopwatch.ElapsedMilliseconds, groupKey, resultList.Count);

                return resultList;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByGroupKeyAsync for GroupKey: {GroupKey}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    groupKey, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history hot records for group key {groupKey}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByGroupKeyAsync for GroupKey: {GroupKey}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    groupKey, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history hot records for group key {groupKey}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByGroupKeyAsync for GroupKey: {GroupKey}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    groupKey, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history hot records for group key {groupKey}", ex);
            }
        }

        /// <summary>
        /// Gets lead history hot records for a specific field name and lead ID.
        /// </summary>
        public async Task<List<LeadHistoryHot>> GetByFieldNameAsync(Guid leadId, string fieldName)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryHot""
                WHERE ""LeadId"" = @LeadId AND ""FieldName"" = @FieldName AND ""IsDeleted"" = false
                ORDER BY ""Version"" DESC, ""ModifiedOn"" DESC";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing GetByFieldNameAsync for LeadId: {LeadId}, FieldName: {FieldName}", leadId, fieldName);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.QueryAsync<LeadHistoryHot>(sql, new { LeadId = leadId, FieldName = fieldName });
                var resultList = result.ToList();

                stopwatch.Stop();
                _logger.LogDebug("GetByFieldNameAsync completed successfully in {ElapsedMs}ms for LeadId: {LeadId}, FieldName: {FieldName}. Records returned: {RecordCount}",
                    stopwatch.ElapsedMilliseconds, leadId, fieldName, resultList.Count);

                return resultList;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in GetByFieldNameAsync for LeadId: {LeadId}, FieldName: {FieldName}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, fieldName, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to retrieve lead history hot records for lead ID {leadId} and field {fieldName}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in GetByFieldNameAsync for LeadId: {LeadId}, FieldName: {FieldName}. Operation: SELECT. ElapsedMs: {ElapsedMs}",
                    leadId, fieldName, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while retrieving lead history hot records for lead ID {leadId} and field {fieldName}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in GetByFieldNameAsync for LeadId: {LeadId}, FieldName: {FieldName}. Operation: SELECT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    leadId, fieldName, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while retrieving lead history hot records for lead ID {leadId} and field {fieldName}", ex);
            }
        }

        /// <summary>
        /// Gets lead history hot records within a date range with pagination.
        /// </summary>
        public async Task<List<LeadHistoryHot>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, int pageNumber = 1, int pageSize = 100)
        {
            const string sql = @"
                SELECT * FROM ""LeadratBlack"".""LeadHistoryHot""
                WHERE ""ModifiedOn"" >= @StartDate AND ""ModifiedOn"" <= @EndDate AND ""IsDeleted"" = false
                ORDER BY ""ModifiedOn"" DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

            var offset = (pageNumber - 1) * pageSize;
            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            var result = await connection.QueryAsync<LeadHistoryHot>(sql, new { StartDate = startDate, EndDate = endDate, Offset = offset, PageSize = pageSize });
            return result.ToList();
        }

        /// <summary>
        /// Gets the total count of lead history hot records.
        /// </summary>
        public async Task<int> GetCountAsync()
        {
            const string sql = @"SELECT COUNT(*) FROM ""LeadratBlack"".""LeadHistoryHot"" WHERE ""IsDeleted"" = false";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.QuerySingleAsync<int>(sql);
        }

        /// <summary>
        /// Gets the count of records for a specific lead ID.
        /// </summary>
        public async Task<int> GetCountByLeadIdAsync(Guid leadId)
        {
            const string sql = @"SELECT COUNT(*) FROM ""LeadratBlack"".""LeadHistoryHot"" WHERE ""LeadId"" = @LeadId AND ""IsDeleted"" = false";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.QuerySingleAsync<int>(sql, new { LeadId = leadId });
        }

        /// <summary>
        /// Inserts a single lead history hot record.
        /// </summary>
        public async Task<int> InsertAsync(LeadHistoryHot record)
        {
            const string sql = @"
                INSERT INTO ""LeadratBlack"".""LeadHistoryHot""
                (""Id"", ""LeadId"", ""FieldName"", ""FieldType"", ""OldValue"", ""NewValue"", ""ModifiedBy"",
                 ""ModifiedOn"", ""LastModifiedById"", ""GroupKey"", ""Version"", ""UserId"", ""TenantId"", ""IsDeleted"")
                VALUES
                (@Id, @LeadId, @FieldName, @FieldType, @OldValue, @NewValue, @ModifiedBy,
                 @ModifiedOn, @LastModifiedById, @GroupKey, @Version, @UserId, @TenantId, @IsDeleted)";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                if (record == null)
                    throw new ArgumentNullException(nameof(record));

                _logger.LogDebug("Executing InsertAsync for LeadHistoryHot with Id: {Id}, LeadId: {LeadId}, FieldName: {FieldName}",
                    record.Id, record.LeadId, record.FieldName);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.ExecuteAsync(sql, record);

                stopwatch.Stop();
                _logger.LogDebug("InsertAsync completed successfully in {ElapsedMs}ms for Id: {Id}. Rows affected: {RowsAffected}",
                    stopwatch.ElapsedMilliseconds, record.Id, result);

                return result;
            }
            catch (NpgsqlException ex) when (ex.SqlState == "23505") // Unique constraint violation
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Duplicate key violation in InsertAsync for Id: {Id}. Operation: INSERT. ElapsedMs: {ElapsedMs}",
                    record?.Id, stopwatch.ElapsedMilliseconds);
                throw new InvalidOperationException($"A lead history hot record with ID {record?.Id} already exists", ex);
            }
            catch (NpgsqlException ex) when (ex.SqlState == "23503") // Foreign key constraint violation
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Foreign key constraint violation in InsertAsync for Id: {Id}. Operation: INSERT. ElapsedMs: {ElapsedMs}",
                    record?.Id, stopwatch.ElapsedMilliseconds);
                throw new InvalidOperationException($"Foreign key constraint violation while inserting lead history hot record with ID {record?.Id}", ex);
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in InsertAsync for Id: {Id}. Operation: INSERT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    record?.Id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to insert lead history hot record with ID {record?.Id}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in InsertAsync for Id: {Id}. Operation: INSERT. ElapsedMs: {ElapsedMs}",
                    record?.Id, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while inserting lead history hot record with ID {record?.Id}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in InsertAsync for Id: {Id}. Operation: INSERT. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    record?.Id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while inserting lead history hot record with ID {record?.Id}", ex);
            }
        }

        /// <summary>
        /// Inserts multiple lead history hot records using bulk operations for optimal performance.
        /// Uses PostgreSQL-specific bulk insert for maximum throughput.
        /// </summary>
        public async Task<int> BulkInsertAsync(IEnumerable<LeadHistoryHot> records)
        {
            if (records == null || !records.Any())
                return 0;

            const string sql = @"
                INSERT INTO ""LeadratBlack"".""LeadHistoryHot""
                (""Id"", ""LeadId"", ""FieldName"", ""FieldType"", ""OldValue"", ""NewValue"", ""ModifiedBy"",
                 ""ModifiedOn"", ""LastModifiedById"", ""GroupKey"", ""Version"", ""UserId"", ""TenantId"", ""IsDeleted"")
                VALUES
                (@Id, @LeadId, @FieldName, @FieldType, @OldValue, @NewValue, @ModifiedBy,
                 @ModifiedOn, @LastModifiedById, @GroupKey, @Version, @UserId, @TenantId, @IsDeleted)";

            var stopwatch = Stopwatch.StartNew();
            var recordList = records.ToList();
            var recordCount = recordList.Count;

            try
            {
                _logger.LogDebug("Executing BulkInsertAsync for {RecordCount} LeadHistoryHot records", recordCount);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();

                // Use transaction for bulk operations to ensure consistency
                using var transaction = connection.BeginTransaction();
                try
                {
                    var result = await connection.ExecuteAsync(sql, recordList, transaction);
                    transaction.Commit();

                    stopwatch.Stop();
                    _logger.LogDebug("BulkInsertAsync completed successfully in {ElapsedMs}ms. Records processed: {RecordCount}, Rows affected: {RowsAffected}",
                        stopwatch.ElapsedMilliseconds, recordCount, result);

                    return result;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    _logger.LogError(ex, "Transaction rolled back in BulkInsertAsync due to error. Records attempted: {RecordCount}", recordCount);
                    throw;
                }
            }
            catch (NpgsqlException ex) when (ex.SqlState == "23505") // Unique constraint violation
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Duplicate key violation in BulkInsertAsync. Operation: BULK INSERT. RecordCount: {RecordCount}. ElapsedMs: {ElapsedMs}",
                    recordCount, stopwatch.ElapsedMilliseconds);
                throw new InvalidOperationException($"Duplicate key violation during bulk insert of {recordCount} lead history hot records", ex);
            }
            catch (NpgsqlException ex) when (ex.SqlState == "23503") // Foreign key constraint violation
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Foreign key constraint violation in BulkInsertAsync. Operation: BULK INSERT. RecordCount: {RecordCount}. ElapsedMs: {ElapsedMs}",
                    recordCount, stopwatch.ElapsedMilliseconds);
                throw new InvalidOperationException($"Foreign key constraint violation during bulk insert of {recordCount} lead history hot records", ex);
            }
            catch (NpgsqlException ex) when (ex.SqlState == "40001") // Deadlock detected
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Deadlock detected in BulkInsertAsync. Operation: BULK INSERT. RecordCount: {RecordCount}. ElapsedMs: {ElapsedMs}",
                    recordCount, stopwatch.ElapsedMilliseconds);
                throw new InvalidOperationException($"Deadlock detected during bulk insert of {recordCount} lead history hot records. Please retry the operation.", ex);
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in BulkInsertAsync. Operation: BULK INSERT. RecordCount: {RecordCount}. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    recordCount, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to bulk insert {recordCount} lead history hot records", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in BulkInsertAsync. Operation: BULK INSERT. RecordCount: {RecordCount}. ElapsedMs: {ElapsedMs}",
                    recordCount, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out during bulk insert of {recordCount} lead history hot records", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in BulkInsertAsync. Operation: BULK INSERT. RecordCount: {RecordCount}. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    recordCount, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred during bulk insert of {recordCount} lead history hot records", ex);
            }
        }

        /// <summary>
        /// Updates a lead history hot record.
        /// </summary>
        public async Task<int> UpdateAsync(LeadHistoryHot record)
        {
            const string sql = @"
                UPDATE ""LeadratBlack"".""LeadHistoryHot""
                SET ""FieldName"" = @FieldName, ""FieldType"" = @FieldType, ""OldValue"" = @OldValue,
                    ""NewValue"" = @NewValue, ""ModifiedBy"" = @ModifiedBy, ""ModifiedOn"" = @ModifiedOn,
                    ""LastModifiedById"" = @LastModifiedById, ""Version"" = @Version, ""UserId"" = @UserId,
                    ""TenantId"" = @TenantId, ""IsDeleted"" = @IsDeleted
                WHERE ""Id"" = @Id";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                if (record == null)
                    throw new ArgumentNullException(nameof(record));

                _logger.LogDebug("Executing UpdateAsync for LeadHistoryHot with Id: {Id}", record.Id);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.ExecuteAsync(sql, record);

                stopwatch.Stop();
                _logger.LogDebug("UpdateAsync completed successfully in {ElapsedMs}ms for Id: {Id}. Rows affected: {RowsAffected}",
                    stopwatch.ElapsedMilliseconds, record.Id, result);

                if (result == 0)
                {
                    _logger.LogWarning("UpdateAsync found no records to update for Id: {Id}", record.Id);
                }

                return result;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in UpdateAsync for Id: {Id}. Operation: UPDATE. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    record?.Id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to update lead history hot record with ID {record?.Id}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in UpdateAsync for Id: {Id}. Operation: UPDATE. ElapsedMs: {ElapsedMs}",
                    record?.Id, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while updating lead history hot record with ID {record?.Id}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in UpdateAsync for Id: {Id}. Operation: UPDATE. ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    record?.Id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while updating lead history hot record with ID {record?.Id}", ex);
            }
        }

        /// <summary>
        /// Soft deletes a lead history hot record by setting IsDeleted = true.
        /// </summary>
        public async Task<int> SoftDeleteAsync(Guid id)
        {
            const string sql = @"UPDATE ""LeadratBlack"".""LeadHistoryHot"" SET ""IsDeleted"" = true WHERE ""Id"" = @Id";

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogDebug("Executing SoftDeleteAsync for LeadHistoryHot with Id: {Id}", id);

                using var connection = await _connectionFactory.CreateOpenConnectionAsync();
                var result = await connection.ExecuteAsync(sql, new { Id = id });

                stopwatch.Stop();
                _logger.LogDebug("SoftDeleteAsync completed successfully in {ElapsedMs}ms for Id: {Id}. Rows affected: {RowsAffected}",
                    stopwatch.ElapsedMilliseconds, id, result);

                if (result == 0)
                {
                    _logger.LogWarning("SoftDeleteAsync found no records to delete for Id: {Id}", id);
                }

                return result;
            }
            catch (NpgsqlException ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Database error in SoftDeleteAsync for Id: {Id}. Operation: UPDATE (SOFT DELETE). ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"Failed to soft delete lead history hot record with ID {id}", ex);
            }
            catch (TimeoutException ex)
            {
                stopwatch.Stop();
                _logger.LogWarning(ex, "Timeout in SoftDeleteAsync for Id: {Id}. Operation: UPDATE (SOFT DELETE). ElapsedMs: {ElapsedMs}",
                    id, stopwatch.ElapsedMilliseconds);
                throw new TimeoutException($"Database operation timed out while soft deleting lead history hot record with ID {id}", ex);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Unexpected error in SoftDeleteAsync for Id: {Id}. Operation: UPDATE (SOFT DELETE). ElapsedMs: {ElapsedMs}. Error: {ErrorMessage}",
                    id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw new InvalidOperationException($"An unexpected error occurred while soft deleting lead history hot record with ID {id}", ex);
            }
        }

        /// <summary>
        /// Hard deletes a lead history hot record from the database.
        /// </summary>
        public async Task<int> DeleteAsync(Guid id)
        {
            const string sql = @"DELETE FROM ""LeadratBlack"".""LeadHistoryHot"" WHERE ""Id"" = @Id";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, new { Id = id });
        }

        /// <summary>
        /// Deletes all records for a specific lead ID.
        /// </summary>
        public async Task<int> DeleteByLeadIdAsync(Guid leadId)
        {
            const string sql = @"DELETE FROM ""LeadratBlack"".""LeadHistoryHot"" WHERE ""LeadId"" = @LeadId";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, new { LeadId = leadId });
        }

        /// <summary>
        /// Archives old records by moving them to warm storage.
        /// This is a placeholder for the archival process - implement based on your archival strategy.
        /// </summary>
        public async Task<int> ArchiveOldRecordsAsync(DateTime cutoffDate)
        {
            // This would typically involve:
            // 1. Selecting records older than cutoffDate
            // 2. Inserting them into LeadHistoryWarm table
            // 3. Deleting them from LeadHistoryHot table
            // For now, we'll just mark them as deleted

            const string sql = @"
                UPDATE ""LeadratBlack"".""LeadHistoryHot""
                SET ""IsDeleted"" = true
                WHERE ""ModifiedOn"" < @CutoffDate AND ""IsDeleted"" = false";

            using var connection = await _connectionFactory.CreateOpenConnectionAsync();
            return await connection.ExecuteAsync(sql, new { CutoffDate = cutoffDate });
        }
    }
}
