﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace LeadHistoryMigration.Application.ExistingHistory
{
    public class LeadHistory
    {
        // Base Entity
        public Guid Id { get; set; }
        public bool IsDeleted { get; set; }

        // Lead History Specific
        #region AuditableBase
        public DateTime CreatedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime>? ModifiedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? AssignedTo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AssignmentType { get; set; }
        public Guid CreatedBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? LastModifiedBy { get; set; }
        #endregion

        [Column(TypeName = "jsonb")]
        public IDictionary<int, EnquiryType>? EnquiredFor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, SaleType>? SaleType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BasePropertyType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SubPropertyType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BHKType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? NoOfBHK { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Name { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Email { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AlternateContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, long>? LowerBudget { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, long>? UpperBudget { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? Area { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Notes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ConfidentialNotes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredState { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, LeadSource>? LeadSource { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Rating { get; set; } // 1,2,3,4,5 stars
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BaseLeadStatus { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SubLeadStatus { get; set; }
        //LeadSubStatus
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? ScheduledDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? LeadNumber { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChosenProject { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChosenProperty { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BookedUnderName { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? RevertDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SoldPrice { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsHighlighted { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsEscalated { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsAboutToConvert { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsIntegrationLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, int>? ShareCount { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsHotLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AssignedToUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AssignedFromUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsColdLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsWarmLead { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, ContactType>? ContactRecords { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Documents { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? LastModifiedByUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsMeetingDone { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? MeetingLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsSiteVisitDone { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? SiteLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsArchived { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? ArchivedBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime>? ArchivedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? RestoredBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime>? RestoredDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Projects { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Properties { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SubSource { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ReferralName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ReferralContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? AgencyName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CompanyName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? PossessionDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? CarpetArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CarpetAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? ConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, int>? ChildLeadsCount { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? SourcingManager { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? ClosingManager { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Profession>? Profession { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerCity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerState { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerLocation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartnerName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartnerExecutiveName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartnerContactNo { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SourcingManagerUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ClosingManagerUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ChannelPartners { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? SecondaryUserId { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SecondaryUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? PickedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, bool>? IsPicked { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? BookedDate { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Guid>? BookedBy { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BookedByUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomFlags { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, UploadType>? UploadType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? UploadTypeName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiryTypes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BHKTypes { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BHKs { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Beds { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Baths { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Floors { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, OfferType>? OfferType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, FurnishStatus>? Furnished { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCities { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredSubCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredTowerName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredStates { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredLocations { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Agencies { get; set; }
        public string? DuplicateLeadVersion { get; set; }
        public Guid UserId { get; set; }
        public Guid LeadId { get; set; }
        // Version index
        public int CurrentVersion { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, LeadAssignmentType>? LeadAssignmentType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Designation { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, BulkType>? BulkCategory { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SecondaryFromUser { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Links { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? BuiltUpArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? BuiltUpAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? SaleableArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? SaleableAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? BuiltUpAreaConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? SaleableAreaConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ReferralEmail { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? EnquiredCountry { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerSubCommunity { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerTowerName { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? CustomerCountry { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Currency { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? PropertyArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? PropertyAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? PropertyAreaConversionFactor { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, double>? NetArea { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? NetAreaUnit { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, float>? NetAreaConversionFactor { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? UnitName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? ClusterName { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Nationality { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? Campaigns { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Purpose>? Purpose { get; set; }

        [Column(TypeName = "jsonb")]
        public IDictionary<int, PossesionType>? PossesionType { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, string>? LandLine { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, Gender>? Gender { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, MaritalStatusType>? MaritalStatus { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? DateOfBirth { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? AppointmentDoneOn { get; set; }
        [Column(TypeName = "jsonb")]
        public IDictionary<int, DateTime?>? AnniversaryDate { get; set; }
    }
}
