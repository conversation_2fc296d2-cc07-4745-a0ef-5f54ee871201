-- =============================================
-- Lead History Migration Database Schema
-- Creates tables for Hot, Warm, and Cold storage tiers
-- Optimized for PostgreSQL with proper indexing
-- =============================================

-- Create LeadHistoryHot table (frequently accessed data)
CREATE TABLE IF NOT EXISTS "LeadHistoryHot" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "LeadId" UUID NOT NULL,
    "FieldName" VARCHAR(100) NOT NULL,
    "FieldType" VARCHAR(50) NOT NULL,
    "OldValue" TEXT,
    "NewValue" TEXT,
    "ModifiedBy" VARCHAR(255),
    "ModifiedOn" TIMESTAMP WITH TIME ZONE,
    "LastModifiedById" UUID,
    "GroupKey" UUID NOT NULL,
    "Version" INTEGER NOT NULL,
    "UserId" UUID,
    "TenantId" VARCHAR(50) NOT NULL DEFAULT 'ratify',
    "IsDeleted" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create LeadHistoryWarm table (moderately accessed data)
CREATE TABLE IF NOT EXISTS "LeadHistoryWarm" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "LeadId" UUID NOT NULL,
    "FieldName" VARCHAR(100) NOT NULL,
    "FieldType" VARCHAR(50) NOT NULL,
    "OldValue" TEXT,
    "NewValue" TEXT,
    "ModifiedBy" VARCHAR(255),
    "ModifiedOn" TIMESTAMP WITH TIME ZONE,
    "LastModifiedById" UUID,
    "GroupKey" UUID NOT NULL,
    "Version" INTEGER NOT NULL,
    "UserId" UUID,
    "TenantId" VARCHAR(50) NOT NULL DEFAULT 'ratify',
    "IsDeleted" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create LeadHistoryCold table (rarely accessed archival data)
CREATE TABLE IF NOT EXISTS "LeadHistoryCold" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "LeadId" UUID NOT NULL,
    "FieldName" VARCHAR(100) NOT NULL,
    "FieldType" VARCHAR(50) NOT NULL,
    "OldValue" TEXT,
    "NewValue" TEXT,
    "ModifiedBy" VARCHAR(255),
    "ModifiedOn" TIMESTAMP WITH TIME ZONE,
    "LastModifiedById" UUID,
    "GroupKey" UUID NOT NULL,
    "Version" INTEGER NOT NULL,
    "UserId" UUID,
    "TenantId" VARCHAR(50) NOT NULL DEFAULT 'ratify',
    "IsDeleted" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- Indexes for LeadHistoryHot (optimized for frequent access)
-- =============================================

-- Primary access patterns
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_LeadId" ON "LeadHistoryHot" ("LeadId") WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_GroupKey" ON "LeadHistoryHot" ("GroupKey") WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_ModifiedOn" ON "LeadHistoryHot" ("ModifiedOn" DESC) WHERE "IsDeleted" = FALSE;

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_LeadId_FieldName" ON "LeadHistoryHot" ("LeadId", "FieldName") WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_LeadId_ModifiedOn" ON "LeadHistoryHot" ("LeadId", "ModifiedOn" DESC) WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_TenantId_ModifiedOn" ON "LeadHistoryHot" ("TenantId", "ModifiedOn" DESC) WHERE "IsDeleted" = FALSE;

-- Performance indexes
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_Version" ON "LeadHistoryHot" ("Version") WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryHot_UserId" ON "LeadHistoryHot" ("UserId") WHERE "IsDeleted" = FALSE;

-- =============================================
-- Indexes for LeadHistoryWarm (moderate access optimization)
-- =============================================

CREATE INDEX IF NOT EXISTS "IX_LeadHistoryWarm_LeadId" ON "LeadHistoryWarm" ("LeadId") WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryWarm_GroupKey" ON "LeadHistoryWarm" ("GroupKey") WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryWarm_ModifiedOn" ON "LeadHistoryWarm" ("ModifiedOn" DESC) WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryWarm_LeadId_ModifiedOn" ON "LeadHistoryWarm" ("LeadId", "ModifiedOn" DESC) WHERE "IsDeleted" = FALSE;

-- =============================================
-- Indexes for LeadHistoryCold (archival access optimization)
-- =============================================

CREATE INDEX IF NOT EXISTS "IX_LeadHistoryCold_LeadId" ON "LeadHistoryCold" ("LeadId") WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryCold_ModifiedOn" ON "LeadHistoryCold" ("ModifiedOn" DESC) WHERE "IsDeleted" = FALSE;
CREATE INDEX IF NOT EXISTS "IX_LeadHistoryCold_GroupKey" ON "LeadHistoryCold" ("GroupKey") WHERE "IsDeleted" = FALSE;

-- =============================================
-- Update triggers for UpdatedAt timestamps
-- =============================================

-- Function to update the UpdatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."UpdatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for each table
CREATE TRIGGER update_leadhistoryhot_updated_at BEFORE UPDATE ON "LeadHistoryHot" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_leadhistorywarm_updated_at BEFORE UPDATE ON "LeadHistoryWarm" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_leadhistorycold_updated_at BEFORE UPDATE ON "LeadHistoryCold" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- Comments for documentation
-- =============================================

COMMENT ON TABLE "LeadHistoryHot" IS 'Frequently accessed lead history records with field-wise change tracking';
COMMENT ON TABLE "LeadHistoryWarm" IS 'Moderately accessed lead history records for intermediate storage';
COMMENT ON TABLE "LeadHistoryCold" IS 'Rarely accessed archival lead history records for long-term storage';

COMMENT ON COLUMN "LeadHistoryHot"."GroupKey" IS 'Groups related field changes from the same transformation batch';
COMMENT ON COLUMN "LeadHistoryHot"."Version" IS 'Version number from the original versioned dictionary data';
COMMENT ON COLUMN "LeadHistoryHot"."FieldName" IS 'Name of the field that changed (e.g., Name, Email, ContactNo)';
COMMENT ON COLUMN "LeadHistoryHot"."FieldType" IS 'Data type of the field for proper handling and validation';
